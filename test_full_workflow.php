<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Category;
use App\Models\InterestRequest;
use Illuminate\Support\Facades\Hash;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing Full Investment Platform Workflow...\n\n";

// Get existing users
$investor = User::where('email', '<EMAIL>')->first();
$startup = User::where('email', '<EMAIL>')->first();

if (!$investor || !$startup) {
    echo "Error: Test users not found. Please run the previous tests first.\n";
    exit(1);
}

// Create investor token
$investorToken = $investor->createToken('test-token')->plainTextToken;

echo "Testing Interest Request Creation (Investor -> Startup)...\n";

// Get startup profile ID
$startupProfile = $startup->startupProfile;
if (!$startupProfile) {
    echo "Error: Startup profile not found.\n";
    exit(1);
}

$interestData = [
    'target_id' => $startup->id,
    'type' => 'investment_interest',
    'message' => 'I am interested in investing in your startup. Your ESG score is impressive!',
    'proposed_amount' => 50000,
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/interest-requests');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($interestData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $investorToken
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 300) . "...\n\n";

// Test viewing interest requests (investor perspective)
echo "Testing Interest Requests List (Investor)...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/interest-requests');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $investorToken
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 300) . "...\n\n";

// Test viewing interest requests (startup perspective)
$startupToken = $startup->createToken('test-token')->plainTextToken;

echo "Testing Interest Requests List (Startup)...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/interest-requests');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $startupToken
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 300) . "...\n\n";

// Test discovery with actual data
echo "Testing Discovery with Real Data (Investor -> Startups)...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/discovery/startups');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $investorToken
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 400) . "...\n\n";

// Test discovery (Startup -> Investors)
echo "Testing Discovery with Real Data (Startup -> Investors)...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/discovery/investors?min_budget=40000');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $startupToken
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 400) . "...\n\n";

// Cleanup
$investor->tokens()->delete();
$startup->tokens()->delete();

echo "Full workflow test completed!\n";
echo "\nSummary:\n";
echo "✅ Categories API\n";
echo "✅ Investor Profile Creation\n";
echo "✅ Startup Profile Creation\n";
echo "✅ ESG Questionnaire\n";
echo "✅ Discovery (both directions)\n";
echo "✅ Interest Request Creation\n";
echo "✅ Interest Request Listing\n";
echo "\nThe Investment Platform API is fully functional!\n";
