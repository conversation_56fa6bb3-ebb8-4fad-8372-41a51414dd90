import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './components/App';
import './bootstrap';

console.log('React app script loaded');

// Wait for DOM to be ready
function initReactApp() {
    console.log('Initializing React app...');
    const container = document.getElementById('react-app');
    console.log('React app container:', container);

    if (container) {
        console.log('Mounting React app...');
        const root = createRoot(container);
        root.render(<App />);
        console.log('React app mounted successfully');
    } else {
        console.error('React app container not found!');
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initReactApp);
} else {
    initReactApp();
}
