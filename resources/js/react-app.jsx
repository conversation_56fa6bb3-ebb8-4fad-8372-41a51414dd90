console.log('=== REACT SCRIPT STARTING ===');

import React from 'react';
import { createRoot } from 'react-dom/client';
// import './bootstrap';

console.log('React app script loaded');

// Simple test component
function TestApp() {
    console.log('TestApp component rendering...');
    return (
        <div style={{ padding: '20px', backgroundColor: '#f0f0f0' }}>
            <h1>React App Test</h1>
            <p>If you can see this, React is working!</p>
        </div>
    );
}

// Wait for DOM to be ready
function initReactApp() {
    console.log('Initializing React app...');
    const container = document.getElementById('react-app');
    console.log('React app container:', container);

    if (container) {
        console.log('Mounting React app...');
        const root = createRoot(container);
        root.render(<TestApp />);
        console.log('React app mounted successfully');
    } else {
        console.error('React app container not found!');
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initReactApp);
} else {
    initReactApp();
}
