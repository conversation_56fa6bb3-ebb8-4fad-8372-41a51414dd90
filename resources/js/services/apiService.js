import axios from 'axios';

class ApiService {
    constructor() {
        this.api = axios.create({
            baseURL: '/api',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });

        // Add CSRF token to requests
        const token = document.querySelector('meta[name="csrf-token"]');
        if (token) {
            this.api.defaults.headers.common['X-CSRF-TOKEN'] = token.getAttribute('content');
        }

        // Response interceptor for error handling
        this.api.interceptors.response.use(
            (response) => response,
            (error) => {
                if (error.response?.status === 401) {
                    // Token expired or invalid
                    this.removeAuthToken();
                    window.location.href = '/login';
                }
                return Promise.reject(error);
            }
        );
    }

    setAuthToken(token) {
        this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }

    removeAuthToken() {
        delete this.api.defaults.headers.common['Authorization'];
    }

    // Generic HTTP methods
    async get(url, config = {}) {
        return this.api.get(url, config);
    }

    async post(url, data = {}, config = {}) {
        return this.api.post(url, data, config);
    }

    async put(url, data = {}, config = {}) {
        return this.api.put(url, data, config);
    }

    async patch(url, data = {}, config = {}) {
        return this.api.patch(url, data, config);
    }

    async delete(url, config = {}) {
        return this.api.delete(url, config);
    }

    // Authentication endpoints
    async login(credentials) {
        return this.post('/login', credentials);
    }

    async register(userData) {
        return this.post('/register', userData);
    }

    async logout() {
        return this.post('/logout');
    }

    async getUser() {
        return this.get('/user');
    }

    // Investment Platform API endpoints
    
    // Categories
    async getCategories() {
        return this.get('/investment/categories');
    }

    // Investor Profile
    async getInvestorProfile() {
        return this.get('/investment/investor-profiles');
    }

    async createInvestorProfile(data) {
        return this.post('/investment/investor-profiles', data);
    }

    async updateInvestorProfile(data) {
        return this.post('/investment/investor-profiles', data);
    }

    // Startup Profile
    async getStartupProfile() {
        return this.get('/investment/startup-profiles');
    }

    async createStartupProfile(data) {
        return this.post('/investment/startup-profiles', data);
    }

    async updateStartupProfile(data) {
        return this.post('/investment/startup-profiles', data);
    }

    // ESG System
    async getESGQuestions() {
        return this.get('/investment/esg/questions');
    }

    async submitESGResponses(data) {
        return this.post('/investment/esg/responses', data);
    }

    async getESGResponses() {
        return this.get('/investment/esg/responses');
    }

    // Discovery
    async discoverStartups(params = {}) {
        return this.get('/investment/discovery/startups', { params });
    }

    async discoverInvestors(params = {}) {
        return this.get('/investment/discovery/investors', { params });
    }

    // Interest Requests
    async getInterestRequests(params = {}) {
        return this.get('/investment/interest-requests', { params });
    }

    async createInterestRequest(data) {
        return this.post('/investment/interest-requests', data);
    }

    async getInterestRequest(id) {
        return this.get(`/investment/interest-requests/${id}`);
    }

    // Subscription System
    async getSubscriptionProducts() {
        return this.get('/subscription-products');
    }

    async getUserSubscriptions() {
        return this.get('/subscriptions');
    }

    async createSubscription(data) {
        return this.post('/subscriptions', data);
    }

    async updateSubscription(id, data) {
        return this.put(`/subscriptions/${id}`, data);
    }

    async cancelSubscription(id) {
        return this.delete(`/subscriptions/${id}`);
    }

    async createStripeCheckoutSession(data) {
        return this.post('/subscriptions/checkout', data);
    }

    async getSubscriptionStatus() {
        return this.get('/subscriptions/status');
    }
}

export const apiService = new ApiService();
