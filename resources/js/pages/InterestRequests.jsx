import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout';
import SubscriptionGate from '../components/SubscriptionGate';
import { apiService } from '../services/apiService';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';

const InterestRequests = () => {
    const { user } = useAuth();
    const [requests, setRequests] = useState([]);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState('sent');

    useEffect(() => {
        fetchRequests();
    }, [activeTab]);

    const fetchRequests = async () => {
        setLoading(true);
        try {
            const response = await apiService.getInterestRequests(activeTab);
            setRequests(response.data.data || []);
        } catch (error) {
            console.error('Failed to fetch requests:', error);
            setRequests([]);
        } finally {
            setLoading(false);
        }
    };

    const handleStatusUpdate = async (requestId, status) => {
        try {
            await apiService.updateInterestRequestStatus(requestId, status);
            fetchRequests(); // Refresh the list
        } catch (error) {
            console.error('Failed to update status:', error);
        }
    };

    const renderRequestCard = (request) => {
        const isInvestor = user?.roles?.includes('investor');
        const isSentTab = activeTab === 'sent';

        return (
            <div key={request.id} className="bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-6">
                <div className="flex justify-between items-start mb-4">
                    <div>
                        <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                            {isSentTab
                                ? (isInvestor ? request.startup?.company_name : request.investor?.full_name)
                                : (isInvestor ? request.startup?.company_name : request.investor?.full_name)
                            }
                        </h3>
                        <p className="text-slate-600 dark:text-slate-400">
                            {request.created_at && new Date(request.created_at).toLocaleDateString()}
                        </p>
                    </div>
                    <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                            request.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                            request.status === 'accepted' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                            request.status === 'rejected' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                            'bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-300'
                        }`}>
                            {request.status}
                        </span>
                    </div>
                </div>

                <p className="text-slate-600 dark:text-slate-400 mb-4">
                    {request.message || 'No message provided'}
                </p>

                {!isSentTab && request.status === 'pending' && (
                    <div className="flex space-x-2">
                        <button
                            onClick={() => handleStatusUpdate(request.id, 'accepted')}
                            className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors text-sm"
                        >
                            Accept
                        </button>
                        <button
                            onClick={() => handleStatusUpdate(request.id, 'rejected')}
                            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors text-sm"
                        >
                            Reject
                        </button>
                    </div>
                )}
            </div>
        );
    };

    return (
        <Layout>
            <div className="space-y-6">
                {/* Page Header */}
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                        Interest Requests
                    </h1>
                    <p className="text-slate-600 dark:text-slate-400 mt-1">
                        Manage your investment interest requests and communications
                    </p>
                </div>

                {/* Tabs */}
                <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700">
                    <div className="border-b border-slate-200 dark:border-slate-700">
                        <nav className="flex space-x-8 px-6">
                            <button
                                onClick={() => setActiveTab('sent')}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'sent'
                                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                                        : 'border-transparent text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300'
                                }`}
                            >
                                Sent Requests
                            </button>
                            <button
                                onClick={() => setActiveTab('received')}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'received'
                                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                                        : 'border-transparent text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300'
                                }`}
                            >
                                Received Requests
                            </button>
                        </nav>
                    </div>

                    <div className="p-6">
                        {loading ? (
                            <div className="flex justify-center py-8">
                                <LoadingSpinner />
                            </div>
                        ) : requests.length > 0 ? (
                            <div className="space-y-4">
                                {requests.map(renderRequestCard)}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <svg className="mx-auto h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                </svg>
                                <h3 className="mt-2 text-sm font-medium text-slate-900 dark:text-white">
                                    No {activeTab} requests yet
                                </h3>
                                <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                                    Your {activeTab} interest requests will appear here.
                                </p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Advanced Analytics - Premium Feature */}
                <SubscriptionGate requiredPlan="premium" feature="Advanced Analytics">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            Request Analytics
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="text-center">
                                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">0</div>
                                <div className="text-sm text-slate-500 dark:text-slate-400 mt-1">Response Rate</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold text-green-600 dark:text-green-400">0</div>
                                <div className="text-sm text-slate-500 dark:text-slate-400 mt-1">Avg. Response Time</div>
                            </div>
                            <div className="text-center">
                                <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">0</div>
                                <div className="text-sm text-slate-500 dark:text-slate-400 mt-1">Success Rate</div>
                            </div>
                        </div>
                    </div>
                </SubscriptionGate>

                {/* Bulk Actions - Enterprise Feature */}
                <SubscriptionGate requiredPlan="enterprise" feature="Bulk Operations">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            Bulk Actions
                        </h2>
                        <p className="text-slate-600 dark:text-slate-400 mb-4">
                            Manage multiple interest requests simultaneously with advanced bulk operations.
                        </p>
                        <div className="flex space-x-3">
                            <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                                Bulk Send
                            </button>
                            <button className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                                Bulk Follow-up
                            </button>
                            <button className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">
                                Bulk Archive
                            </button>
                        </div>
                    </div>
                </SubscriptionGate>
            </div>
        </Layout>
    );
};

export default InterestRequests;
