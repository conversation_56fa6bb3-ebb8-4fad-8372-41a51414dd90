import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Link } from 'react-router-dom';

const StartupDashboard = () => {
    const { user, logout } = useAuth();

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <header className="bg-white shadow">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-6">
                        <h1 className="text-3xl font-bold text-gray-900">
                            Startup Dashboard
                        </h1>
                        <div className="flex items-center space-x-4">
                            <span className="text-gray-700">Welcome, {user?.name}</span>
                            <button
                                onClick={logout}
                                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
                            >
                                Logout
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div className="px-4 py-6 sm:px-0">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {/* Profile Card */}
                        <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="p-5">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                            <span className="text-white font-semibold">🏢</span>
                                        </div>
                                    </div>
                                    <div className="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                Company Profile
                                            </dt>
                                            <dd className="text-lg font-medium text-gray-900">
                                                Manage your startup profile
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                                <div className="mt-4">
                                    <Link
                                        to="/startup/profile"
                                        className="text-blue-600 hover:text-blue-500 text-sm font-medium"
                                    >
                                        View Profile →
                                    </Link>
                                </div>
                            </div>
                        </div>

                        {/* ESG Card */}
                        <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="p-5">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                            <span className="text-white font-semibold">🌱</span>
                                        </div>
                                    </div>
                                    <div className="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                ESG Assessment
                                            </dt>
                                            <dd className="text-lg font-medium text-gray-900">
                                                Complete ESG questionnaire
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                                <div className="mt-4">
                                    <Link
                                        to="/startup/esg"
                                        className="text-blue-600 hover:text-blue-500 text-sm font-medium"
                                    >
                                        Take Assessment →
                                    </Link>
                                </div>
                            </div>
                        </div>

                        {/* Discovery Card */}
                        <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="p-5">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                            <span className="text-white font-semibold">🔍</span>
                                        </div>
                                    </div>
                                    <div className="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                Investor Discovery
                                            </dt>
                                            <dd className="text-lg font-medium text-gray-900">
                                                Find potential investors
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                                <div className="mt-4">
                                    <Link
                                        to="/discovery"
                                        className="text-blue-600 hover:text-blue-500 text-sm font-medium"
                                    >
                                        Discover Investors →
                                    </Link>
                                </div>
                            </div>
                        </div>

                        {/* Interest Requests Card */}
                        <div className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="p-5">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0">
                                        <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                                            <span className="text-white font-semibold">📋</span>
                                        </div>
                                    </div>
                                    <div className="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt className="text-sm font-medium text-gray-500 truncate">
                                                Interest Requests
                                            </dt>
                                            <dd className="text-lg font-medium text-gray-900">
                                                View investor interests
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                                <div className="mt-4">
                                    <Link
                                        to="/interest-requests"
                                        className="text-blue-600 hover:text-blue-500 text-sm font-medium"
                                    >
                                        View Requests →
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Quick Stats */}
                    <div className="mt-8">
                        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Stats</h2>
                        <div className="bg-white shadow rounded-lg p-6">
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-blue-600">0</div>
                                    <div className="text-sm text-gray-500">Profile Completion</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-green-600">0</div>
                                    <div className="text-sm text-gray-500">ESG Score</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-purple-600">0</div>
                                    <div className="text-sm text-gray-500">Investor Interests</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-orange-600">0</div>
                                    <div className="text-sm text-gray-500">Profile Views</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
};

export default StartupDashboard;
