import React, { useState, useEffect } from 'react';
import Layout from '../../components/Layout';
import SubscriptionGate from '../../components/SubscriptionGate';
import { apiService } from '../../services/apiService';
import LoadingSpinner from '../../components/LoadingSpinner';

const InvestorProfile = () => {
    const [profile, setProfile] = useState({
        full_name: '',
        investment_focus: '',
        min_investment: '',
        max_investment: '',
        risk_tolerance: ''
    });
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [categories, setCategories] = useState([]);

    useEffect(() => {
        fetchProfile();
        fetchCategories();
    }, []);

    const fetchProfile = async () => {
        try {
            const response = await apiService.getInvestorProfile();
            if (response.data.data) {
                setProfile(response.data.data);
            }
        } catch (error) {
            console.error('Failed to fetch profile:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchCategories = async () => {
        try {
            const response = await apiService.getCategories();
            setCategories(response.data.data || []);
        } catch (error) {
            console.error('Failed to fetch categories:', error);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setProfile(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleSave = async () => {
        setSaving(true);
        try {
            await apiService.updateInvestorProfile(profile);
            alert('Profile saved successfully!');
        } catch (error) {
            console.error('Failed to save profile:', error);
            alert('Failed to save profile. Please try again.');
        } finally {
            setSaving(false);
        }
    };

    if (loading) {
        return (
            <Layout>
                <LoadingSpinner />
            </Layout>
        );
    }

    return (
        <Layout>
            <div className="space-y-6">
                {/* Page Header */}
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                        Investor Profile
                    </h1>
                    <p className="text-slate-600 dark:text-slate-400 mt-1">
                        Manage your investor profile and preferences
                    </p>
                </div>

                {/* Basic Profile Information - Available to all users */}
                <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                    <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                        Basic Information
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                Full Name
                            </label>
                            <input
                                type="text"
                                name="full_name"
                                value={profile.full_name}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                placeholder="Enter your full name"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                Investment Focus
                            </label>
                            <select
                                name="investment_focus"
                                value={profile.investment_focus}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                            >
                                <option value="">Select investment focus</option>
                                {categories.map(category => (
                                    <option key={category.id} value={category.name}>
                                        {category.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                </div>

                {/* Advanced Portfolio Settings - Premium Feature */}
                <SubscriptionGate requiredPlan="premium" feature="Advanced Portfolio Management">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            Portfolio Management
                        </h2>
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                    Investment Range
                                </label>
                                <div className="grid grid-cols-2 gap-4">
                                    <input
                                        type="number"
                                        name="min_investment"
                                        value={profile.min_investment}
                                        onChange={handleInputChange}
                                        placeholder="Min amount"
                                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                    />
                                    <input
                                        type="number"
                                        name="max_investment"
                                        value={profile.max_investment}
                                        onChange={handleInputChange}
                                        placeholder="Max amount"
                                        className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                    />
                                </div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                                    Risk Tolerance
                                </label>
                                <select
                                    name="risk_tolerance"
                                    value={profile.risk_tolerance}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-slate-700 dark:text-white"
                                >
                                    <option value="">Select risk tolerance</option>
                                    <option value="conservative">Conservative</option>
                                    <option value="moderate">Moderate</option>
                                    <option value="aggressive">Aggressive</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </SubscriptionGate>

                {/* AI Investment Insights - Enterprise Feature */}
                <SubscriptionGate requiredPlan="enterprise" feature="AI Investment Insights">
                    <div className="bg-white dark:bg-slate-800 shadow-sm rounded-lg border border-slate-200 dark:border-slate-700 p-6">
                        <h2 className="text-lg font-medium text-slate-900 dark:text-white mb-4">
                            AI Investment Insights
                        </h2>
                        <p className="text-slate-600 dark:text-slate-400 mb-4">
                            Get personalized investment recommendations and market insights powered by AI.
                        </p>
                        <div className="space-y-3">
                            <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                                    Portfolio Optimization
                                </span>
                                <button className="text-blue-600 dark:text-blue-400 text-sm hover:underline">
                                    View Insights
                                </button>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                                <span className="text-sm font-medium text-green-900 dark:text-green-100">
                                    Market Trend Analysis
                                </span>
                                <button className="text-green-600 dark:text-green-400 text-sm hover:underline">
                                    View Analysis
                                </button>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-md">
                                <span className="text-sm font-medium text-purple-900 dark:text-purple-100">
                                    Risk Assessment
                                </span>
                                <button className="text-purple-600 dark:text-purple-400 text-sm hover:underline">
                                    View Assessment
                                </button>
                            </div>
                        </div>
                    </div>
                </SubscriptionGate>

                {/* Save Button */}
                <div className="flex justify-end">
                    <button
                        onClick={handleSave}
                        disabled={saving}
                        className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {saving ? 'Saving...' : 'Save Profile'}
                    </button>
                </div>
            </div>
        </Layout>
    );
};

export default InvestorProfile;
