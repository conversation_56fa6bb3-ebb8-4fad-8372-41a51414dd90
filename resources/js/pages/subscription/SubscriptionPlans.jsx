import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import apiService from '../../services/apiService';
import Layout from '../../components/Layout';

const SubscriptionPlans = () => {
    const { user } = useAuth();
    const [plans, setPlans] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selectedRole, setSelectedRole] = useState('investor');

    useEffect(() => {
        fetchPlans();
    }, []);

    const fetchPlans = async () => {
        try {
            setLoading(true);
            const response = await apiService.get('/subscription-products/public');
            setPlans(response.data.data || []);
        } catch (err) {
            setError('Failed to load subscription plans');
            console.error('Error fetching plans:', err);
        } finally {
            setLoading(false);
        }
    };

    const getFilteredPlans = () => {
        return plans.filter(plan => 
            plan.name.toLowerCase().includes(selectedRole.toLowerCase())
        );
    };

    const handleSelectPlan = async (plan) => {
        try {
            setLoading(true);

            // Create subscription with Stripe checkout
            const response = await apiService.post('/subscriptions', {
                subscription_product_id: plan.id
            });

            if (response.data.data && response.data.data.checkout_url) {
                // Redirect to Stripe Checkout
                window.location.href = response.data.data.checkout_url;
            } else {
                setError('Failed to create checkout session');
            }
        } catch (err) {
            setError('Failed to start subscription process');
            console.error('Error selecting plan:', err);
        } finally {
            setLoading(false);
        }
    };

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    const getBillingCycleText = (cycle) => {
        switch (cycle) {
            case 'monthly': return '/month';
            case 'yearly': return '/year';
            case 'quarterly': return '/quarter';
            default: return `/${cycle}`;
        }
    };

    const getPlanColor = (planName) => {
        if (planName.toLowerCase().includes('basic')) return 'blue';
        if (planName.toLowerCase().includes('premium')) return 'purple';
        if (planName.toLowerCase().includes('enterprise')) return 'green';
        return 'gray';
    };

    const getColorClasses = (color) => {
        const colors = {
            blue: {
                border: 'border-blue-200',
                bg: 'bg-blue-50',
                text: 'text-blue-600',
                button: 'bg-blue-600 hover:bg-blue-700 text-white',
                badge: 'bg-blue-100 text-blue-800'
            },
            purple: {
                border: 'border-purple-200',
                bg: 'bg-purple-50',
                text: 'text-purple-600',
                button: 'bg-purple-600 hover:bg-purple-700 text-white',
                badge: 'bg-purple-100 text-purple-800'
            },
            green: {
                border: 'border-green-200',
                bg: 'bg-green-50',
                text: 'text-green-600',
                button: 'bg-green-600 hover:bg-green-700 text-white',
                badge: 'bg-green-100 text-green-800'
            },
            gray: {
                border: 'border-gray-200',
                bg: 'bg-gray-50',
                text: 'text-gray-600',
                button: 'bg-gray-600 hover:bg-gray-700 text-white',
                badge: 'bg-gray-100 text-gray-800'
            }
        };
        return colors[color] || colors.gray;
    };

    if (loading) {
        return (
            <Layout>
                <div className="flex items-center justify-center min-h-96">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                </div>
            </Layout>
        );
    }

    if (error) {
        return (
            <Layout>
                <div className="text-center py-12">
                    <div className="text-red-600 mb-4">{error}</div>
                    <button 
                        onClick={fetchPlans}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                    >
                        Try Again
                    </button>
                </div>
            </Layout>
        );
    }

    const filteredPlans = getFilteredPlans();

    return (
        <Layout>
            <div className="space-y-6">
                {/* Header */}
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-900 mb-4">
                        Choose Your Subscription Plan
                    </h1>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                        Select the perfect plan for your investment journey. 
                        Upgrade or downgrade at any time.
                    </p>
                </div>

                {/* Role Selector */}
                <div className="flex justify-center">
                    <div className="bg-gray-100 p-1 rounded-lg">
                        <button
                            onClick={() => setSelectedRole('investor')}
                            className={`px-6 py-2 rounded-md font-medium transition-colors ${
                                selectedRole === 'investor'
                                    ? 'bg-white text-blue-600 shadow-sm'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            Investor Plans
                        </button>
                        <button
                            onClick={() => setSelectedRole('startup')}
                            className={`px-6 py-2 rounded-md font-medium transition-colors ${
                                selectedRole === 'startup'
                                    ? 'bg-white text-blue-600 shadow-sm'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            Startup Plans
                        </button>
                    </div>
                </div>

                {/* Plans Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                    {filteredPlans.map((plan) => {
                        const color = getPlanColor(plan.name);
                        const colorClasses = getColorClasses(color);
                        const isPopular = plan.name.toLowerCase().includes('premium');

                        return (
                            <div
                                key={plan.id}
                                className={`relative bg-white rounded-xl shadow-lg border-2 ${colorClasses.border} hover:shadow-xl transition-shadow`}
                            >
                                {isPopular && (
                                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                        <span className={`px-4 py-1 rounded-full text-sm font-medium ${colorClasses.badge}`}>
                                            Most Popular
                                        </span>
                                    </div>
                                )}

                                <div className="p-8">
                                    {/* Plan Header */}
                                    <div className="text-center mb-6">
                                        <h3 className="text-xl font-bold text-gray-900 mb-2">
                                            {plan.name}
                                        </h3>
                                        <p className="text-gray-600 text-sm mb-4">
                                            {plan.description}
                                        </p>
                                        <div className="flex items-baseline justify-center">
                                            <span className="text-4xl font-bold text-gray-900">
                                                {formatPrice(plan.price)}
                                            </span>
                                            <span className="text-gray-600 ml-1">
                                                {getBillingCycleText(plan.billing_cycle)}
                                            </span>
                                        </div>
                                    </div>

                                    {/* Features List */}
                                    <div className="mb-8">
                                        <ul className="space-y-3">
                                            {plan.features?.map((feature, index) => (
                                                <li key={index} className="flex items-start">
                                                    <svg
                                                        className={`w-5 h-5 ${colorClasses.text} mr-3 mt-0.5 flex-shrink-0`}
                                                        fill="currentColor"
                                                        viewBox="0 0 20 20"
                                                    >
                                                        <path
                                                            fillRule="evenodd"
                                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                            clipRule="evenodd"
                                                        />
                                                    </svg>
                                                    <span className="text-gray-700 text-sm">
                                                        {feature}
                                                    </span>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>

                                    {/* CTA Button */}
                                    <button
                                        onClick={() => handleSelectPlan(plan)}
                                        className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${colorClasses.button}`}
                                    >
                                        Choose {plan.name}
                                    </button>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {/* No Plans Message */}
                {filteredPlans.length === 0 && (
                    <div className="text-center py-12">
                        <p className="text-gray-600">
                            No {selectedRole} plans available at the moment.
                        </p>
                    </div>
                )}
            </div>
        </Layout>
    );
};

export default SubscriptionPlans;
