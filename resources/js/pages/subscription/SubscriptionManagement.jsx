import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import apiService from '../../services/apiService';
import Layout from '../../components/Layout';

const SubscriptionManagement = () => {
    const { user } = useAuth();
    const [subscription, setSubscription] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [actionLoading, setActionLoading] = useState(false);

    useEffect(() => {
        fetchSubscription();
    }, []);

    const fetchSubscription = async () => {
        try {
            setLoading(true);
            const response = await apiService.get('/subscriptions');
            const subscriptions = response.data.data || [];
            // Get the active subscription
            const activeSubscription = subscriptions.find(sub => sub.status === 'active');
            setSubscription(activeSubscription || null);
        } catch (err) {
            setError('Failed to load subscription information');
            console.error('Error fetching subscription:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleCancelSubscription = async () => {
        if (!subscription || !confirm('Are you sure you want to cancel your subscription?')) {
            return;
        }

        try {
            setActionLoading(true);
            await apiService.post(`/subscriptions/${subscription.id}/cancel`);
            await fetchSubscription(); // Refresh subscription data
        } catch (err) {
            setError('Failed to cancel subscription');
            console.error('Error canceling subscription:', err);
        } finally {
            setActionLoading(false);
        }
    };

    const handleResumeSubscription = async () => {
        if (!subscription) return;

        try {
            setActionLoading(true);
            await apiService.post(`/subscriptions/${subscription.id}/resume`);
            await fetchSubscription(); // Refresh subscription data
        } catch (err) {
            setError('Failed to resume subscription');
            console.error('Error resuming subscription:', err);
        } finally {
            setActionLoading(false);
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const formatPrice = (price) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'canceled': return 'bg-red-100 text-red-800';
            case 'past_due': return 'bg-yellow-100 text-yellow-800';
            case 'trialing': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    if (loading) {
        return (
            <Layout>
                <div className="flex items-center justify-center min-h-96">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                </div>
            </Layout>
        );
    }

    return (
        <Layout>
            <div className="space-y-6">
                {/* Header */}
                <div>
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        Subscription Management
                    </h1>
                    <p className="text-gray-600">
                        Manage your subscription plan and billing information.
                    </p>
                </div>

                {error && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4">
                        <div className="text-red-800">{error}</div>
                    </div>
                )}

                {/* Current Subscription */}
                {subscription ? (
                    <div className="bg-white rounded-lg shadow-md border border-gray-200">
                        <div className="p-6">
                            <div className="flex items-center justify-between mb-6">
                                <h2 className="text-xl font-semibold text-gray-900">
                                    Current Subscription
                                </h2>
                                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
                                    {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                                </span>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Plan Details */}
                                <div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                                        Plan Details
                                    </h3>
                                    <div className="space-y-3">
                                        <div>
                                            <span className="text-sm text-gray-600">Plan Name:</span>
                                            <p className="font-medium text-gray-900">
                                                {subscription.subscription_product?.name}
                                            </p>
                                        </div>
                                        <div>
                                            <span className="text-sm text-gray-600">Price:</span>
                                            <p className="font-medium text-gray-900">
                                                {formatPrice(subscription.amount)} / {subscription.subscription_product?.billing_cycle}
                                            </p>
                                        </div>
                                        <div>
                                            <span className="text-sm text-gray-600">Description:</span>
                                            <p className="text-gray-700">
                                                {subscription.subscription_product?.description}
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Billing Information */}
                                <div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                                        Billing Information
                                    </h3>
                                    <div className="space-y-3">
                                        <div>
                                            <span className="text-sm text-gray-600">Current Period:</span>
                                            <p className="font-medium text-gray-900">
                                                {formatDate(subscription.current_period_start)} - {formatDate(subscription.current_period_end)}
                                            </p>
                                        </div>
                                        <div>
                                            <span className="text-sm text-gray-600">Next Billing Date:</span>
                                            <p className="font-medium text-gray-900">
                                                {formatDate(subscription.current_period_end)}
                                            </p>
                                        </div>
                                        {subscription.trial_end && (
                                            <div>
                                                <span className="text-sm text-gray-600">Trial Ends:</span>
                                                <p className="font-medium text-gray-900">
                                                    {formatDate(subscription.trial_end)}
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Features */}
                            {subscription.subscription_product?.features && (
                                <div className="mt-6">
                                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                                        Plan Features
                                    </h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                        {subscription.subscription_product.features.map((feature, index) => (
                                            <div key={index} className="flex items-center">
                                                <svg
                                                    className="w-4 h-4 text-green-500 mr-2 flex-shrink-0"
                                                    fill="currentColor"
                                                    viewBox="0 0 20 20"
                                                >
                                                    <path
                                                        fillRule="evenodd"
                                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                        clipRule="evenodd"
                                                    />
                                                </svg>
                                                <span className="text-sm text-gray-700">{feature}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* Actions */}
                            <div className="mt-8 flex flex-wrap gap-4">
                                <a
                                    href="/app/subscription/plans"
                                    className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                                >
                                    Upgrade Plan
                                </a>
                                
                                {subscription.status === 'active' && (
                                    <button
                                        onClick={handleCancelSubscription}
                                        disabled={actionLoading}
                                        className="bg-red-600 text-white px-6 py-2 rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
                                    >
                                        {actionLoading ? 'Canceling...' : 'Cancel Subscription'}
                                    </button>
                                )}

                                {subscription.status === 'canceled' && (
                                    <button
                                        onClick={handleResumeSubscription}
                                        disabled={actionLoading}
                                        className="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                                    >
                                        {actionLoading ? 'Resuming...' : 'Resume Subscription'}
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>
                ) : (
                    /* No Subscription */
                    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-8 text-center">
                        <div className="mb-6">
                            <svg
                                className="w-16 h-16 text-gray-400 mx-auto mb-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                            <h3 className="text-xl font-medium text-gray-900 mb-2">
                                No Active Subscription
                            </h3>
                            <p className="text-gray-600">
                                You don't have an active subscription. Choose a plan to get started.
                            </p>
                        </div>
                        <a
                            href="/app/subscription/plans"
                            className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors inline-block"
                        >
                            View Subscription Plans
                        </a>
                    </div>
                )}
            </div>
        </Layout>
    );
};

export default SubscriptionManagement;
