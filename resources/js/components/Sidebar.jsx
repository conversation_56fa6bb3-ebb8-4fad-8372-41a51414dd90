import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Sidebar = () => {
    const { user, hasRole } = useAuth();
    const location = useLocation();
    const [expandedMenus, setExpandedMenus] = useState({});

    const toggleMenu = (menuKey) => {
        setExpandedMenus(prev => ({
            ...prev,
            [menuKey]: !prev[menuKey]
        }));
    };

    const isActive = (path) => {
        return location.pathname === path;
    };

    const isMenuActive = (paths) => {
        return paths.some(path => location.pathname.startsWith(path));
    };

    // Define menu items based on user roles
    const getMenuItems = () => {
        const menuItems = [];

        // Dashboard section - available to all authenticated users
        menuItems.push({
            title: 'DASHBOARD',
            items: [
                {
                    key: 'dashboard',
                    label: 'Dashboard',
                    icon: 'heroicons-outline:home',
                    path: hasRole('investor') ? '/app/investor/dashboard' : 
                          hasRole('startup') ? '/app/startup/dashboard' : 
                          hasRole('analyst') ? '/app/analyst/dashboard' : '/app'
                }
            ]
        });

        // Investment Platform section
        if (hasRole('investor')) {
            menuItems.push({
                title: 'INVESTOR',
                items: [
                    {
                        key: 'investor-profile',
                        label: 'Profile',
                        icon: 'heroicons-outline:user',
                        path: '/app/investor/profile'
                    },
                    {
                        key: 'discovery',
                        label: 'Discover Startups',
                        icon: 'heroicons-outline:search',
                        path: '/app/discovery'
                    },
                    {
                        key: 'interest-requests',
                        label: 'Interest Requests',
                        icon: 'heroicons-outline:clipboard-list',
                        path: '/app/interest-requests'
                    }
                ]
            });
        }

        if (hasRole('startup')) {
            menuItems.push({
                title: 'STARTUP',
                items: [
                    {
                        key: 'startup-profile',
                        label: 'Profile',
                        icon: 'heroicons-outline:user',
                        path: '/app/startup/profile'
                    },
                    {
                        key: 'esg-questionnaire',
                        label: 'ESG Assessment',
                        icon: 'heroicons-outline:clipboard-check',
                        path: '/app/startup/esg-questionnaire'
                    },
                    {
                        key: 'discovery',
                        label: 'Discover Investors',
                        icon: 'heroicons-outline:search',
                        path: '/app/discovery'
                    },
                    {
                        key: 'interest-requests',
                        label: 'Interest Requests',
                        icon: 'heroicons-outline:clipboard-list',
                        path: '/app/interest-requests'
                    }
                ]
            });
        }

        if (hasRole('analyst')) {
            menuItems.push({
                title: 'ANALYST',
                items: [
                    {
                        key: 'discovery',
                        label: 'Platform Overview',
                        icon: 'heroicons-outline:chart-bar',
                        path: '/app/discovery'
                    },
                    {
                        key: 'interest-requests',
                        label: 'Review Requests',
                        icon: 'heroicons-outline:clipboard-list',
                        path: '/app/interest-requests'
                    }
                ]
            });
        }

        // Subscription section - available to investors and startups
        if (hasRole('investor') || hasRole('startup')) {
            menuItems.push({
                title: 'SUBSCRIPTION',
                items: [
                    {
                        key: 'subscription-manage',
                        label: 'My Subscription',
                        icon: 'heroicons-outline:credit-card',
                        path: '/app/subscription/manage'
                    },
                    {
                        key: 'subscription-plans',
                        label: 'View Plans',
                        icon: 'heroicons-outline:view-grid',
                        path: '/app/subscription/plans'
                    }
                ]
            });
        }

        return menuItems;
    };

    const menuItems = getMenuItems();

    return (
        <div className="sidebar-wrapper group w-0 hidden xl:w-[248px] xl:block">
            <div className="logo-segment">
                {/* Application Logo */}
                <Link to="/app" className="flex items-center space-x-2 p-4">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold text-sm">IP</span>
                    </div>
                    <span className="text-xl font-bold text-slate-900 dark:text-white">
                        Investment Platform
                    </span>
                </Link>
            </div>

            <div className="sidebar-menus bg-white dark:bg-slate-800 py-2 px-4 h-[calc(100%-80px)] z-50 overflow-y-auto">
                <ul className="sidebar-menu">
                    {menuItems.map((section, sectionIndex) => (
                        <React.Fragment key={sectionIndex}>
                            <li className="sidebar-menu-title text-xs font-semibold text-slate-400 uppercase tracking-wider mb-2 mt-4">
                                {section.title}
                            </li>
                            {section.items.map((item) => (
                                <li key={item.key} className={`mb-1 ${isActive(item.path) ? 'active' : ''}`}>
                                    <Link
                                        to={item.path}
                                        className={`navItem flex items-center space-x-3 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-150 ${
                                            isActive(item.path)
                                                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                                                : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50 dark:text-slate-300 dark:hover:text-white dark:hover:bg-slate-700'
                                        }`}
                                    >
                                        <span className="flex items-center">
                                            <i className={`iconify nav-icon text-lg ${item.icon}`} data-icon={item.icon}></i>
                                            <span className="ml-3">{item.label}</span>
                                        </span>
                                    </Link>
                                </li>
                            ))}
                        </React.Fragment>
                    ))}
                </ul>

                {/* User Info Card */}
                <div className="bg-slate-900 mb-4 mt-8 p-4 relative text-center rounded-2xl text-white">
                    <div className="w-12 h-12 bg-blue-600 rounded-full mx-auto mb-3 flex items-center justify-center">
                        <span className="text-white font-bold text-lg">
                            {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                        </span>
                    </div>
                    <div className="max-w-[160px] mx-auto">
                        <div className="font-medium text-sm mb-1">{user?.name}</div>
                        <div className="text-xs font-light capitalize">
                            {user?.role} Account
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Sidebar;
