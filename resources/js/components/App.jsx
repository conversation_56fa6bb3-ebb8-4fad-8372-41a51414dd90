import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from '../contexts/AuthContext';
import ProtectedRoute from './ProtectedRoute';
import PublicRoute from './PublicRoute';

// Pages
import Login from '../pages/auth/Login';
import Register from '../pages/auth/Register';
import InvestorDashboard from '../pages/investor/Dashboard';
import StartupDashboard from '../pages/startup/Dashboard';
import AnalystDashboard from '../pages/analyst/Dashboard';
import InvestorProfile from '../pages/investor/Profile';
import StartupProfile from '../pages/startup/Profile';
import Discovery from '../pages/Discovery';
import InterestRequests from '../pages/InterestRequests';
import ESGQuestionnaire from '../pages/startup/ESGQuestionnaire';
import SubscriptionPlans from '../pages/subscription/SubscriptionPlans';
import SubscriptionManagement from '../pages/subscription/SubscriptionManagement';
import SubscriptionSuccess from '../pages/subscription/SubscriptionSuccess';

function App() {
    console.log('App component rendering...');
    return (
        <AuthProvider>
            <Router basename="/app">
                <div className="min-h-screen bg-gray-50">
                    <Routes>
                        {/* Public Routes */}
                        <Route 
                            path="/login" 
                            element={
                                <PublicRoute>
                                    <Login />
                                </PublicRoute>
                            } 
                        />
                        <Route 
                            path="/register" 
                            element={
                                <PublicRoute>
                                    <Register />
                                </PublicRoute>
                            } 
                        />

                        {/* Protected Routes - Investor */}
                        <Route 
                            path="/investor/dashboard" 
                            element={
                                <ProtectedRoute allowedRoles={['investor']}>
                                    <InvestorDashboard />
                                </ProtectedRoute>
                            } 
                        />
                        <Route 
                            path="/investor/profile" 
                            element={
                                <ProtectedRoute allowedRoles={['investor']}>
                                    <InvestorProfile />
                                </ProtectedRoute>
                            } 
                        />

                        {/* Protected Routes - Startup */}
                        <Route 
                            path="/startup/dashboard" 
                            element={
                                <ProtectedRoute allowedRoles={['startup']}>
                                    <StartupDashboard />
                                </ProtectedRoute>
                            } 
                        />
                        <Route 
                            path="/startup/profile" 
                            element={
                                <ProtectedRoute allowedRoles={['startup']}>
                                    <StartupProfile />
                                </ProtectedRoute>
                            } 
                        />
                        <Route 
                            path="/startup/esg" 
                            element={
                                <ProtectedRoute allowedRoles={['startup']}>
                                    <ESGQuestionnaire />
                                </ProtectedRoute>
                            } 
                        />

                        {/* Protected Routes - Analyst */}
                        <Route 
                            path="/analyst/dashboard" 
                            element={
                                <ProtectedRoute allowedRoles={['analyst']}>
                                    <AnalystDashboard />
                                </ProtectedRoute>
                            } 
                        />

                        {/* Shared Protected Routes */}
                        <Route
                            path="/discovery"
                            element={
                                <ProtectedRoute allowedRoles={['investor', 'startup']}>
                                    <Discovery />
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/interest-requests"
                            element={
                                <ProtectedRoute allowedRoles={['investor', 'startup']}>
                                    <InterestRequests />
                                </ProtectedRoute>
                            }
                        />

                        {/* Subscription Routes */}
                        <Route
                            path="/subscription/plans"
                            element={
                                <ProtectedRoute allowedRoles={['investor', 'startup']}>
                                    <SubscriptionPlans />
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/subscription/manage"
                            element={
                                <ProtectedRoute allowedRoles={['investor', 'startup']}>
                                    <SubscriptionManagement />
                                </ProtectedRoute>
                            }
                        />
                        <Route
                            path="/subscription/success"
                            element={
                                <ProtectedRoute allowedRoles={['investor', 'startup']}>
                                    <SubscriptionSuccess />
                                </ProtectedRoute>
                            }
                        />

                        {/* Default redirect */}
                        <Route path="/" element={<Navigate to="/login" replace />} />
                    </Routes>
                </div>
            </Router>
        </AuthProvider>
    );
}

export default App;
