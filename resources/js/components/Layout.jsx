import React from 'react';
import Sidebar from './Sidebar';
import { useAuth } from '../contexts/AuthContext';

const Layout = ({ children }) => {
    const { user, logout } = useAuth();

    return (
        <div className="app-wrapper">
            {/* Sidebar */}
            <Sidebar />

            <div className="flex flex-col justify-between min-h-screen">
                <div>
                    {/* Header */}
                    <header className="bg-white dark:bg-slate-800 shadow-sm border-b border-slate-200 dark:border-slate-700">
                        <div className="content-wrapper transition-all duration-150 ltr:ml-0 xl:ltr:ml-[248px] rtl:mr-0 xl:rtl:mr-[248px]">
                            <div className="flex items-center justify-between px-6 py-4">
                                <div className="flex items-center space-x-4">
                                    {/* Mobile menu button */}
                                    <button className="xl:hidden p-2 rounded-md text-slate-600 hover:text-slate-900 hover:bg-slate-100">
                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                        </svg>
                                    </button>
                                    
                                    {/* Page title will be set by individual pages */}
                                    <div className="hidden md:block">
                                        <h1 className="text-xl font-semibold text-slate-900 dark:text-white">
                                            Investment Platform
                                        </h1>
                                    </div>
                                </div>

                                {/* User menu */}
                                <div className="flex items-center space-x-4">
                                    <div className="hidden md:flex items-center space-x-3">
                                        <div className="text-right">
                                            <div className="text-sm font-medium text-slate-900 dark:text-white">
                                                {user?.name}
                                            </div>
                                            <div className="text-xs text-slate-500 dark:text-slate-400 capitalize">
                                                {user?.role}
                                            </div>
                                        </div>
                                        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                                            <span className="text-white font-medium text-sm">
                                                {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <button
                                        onClick={logout}
                                        className="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-900 hover:bg-slate-100 rounded-md transition-colors duration-150"
                                    >
                                        Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </header>

                    {/* Main content */}
                    <div className="content-wrapper transition-all duration-150 ltr:ml-0 xl:ltr:ml-[248px] rtl:mr-0 xl:rtl:mr-[248px]">
                        <div className="page-content">
                            <div className="transition-all duration-150 container-fluid">
                                <main className="p-6">
                                    {children}
                                </main>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <footer className="bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700">
                    <div className="content-wrapper transition-all duration-150 ltr:ml-0 xl:ltr:ml-[248px] rtl:mr-0 xl:rtl:mr-[248px]">
                        <div className="px-6 py-4">
                            <div className="text-center text-sm text-slate-500 dark:text-slate-400">
                                Copyright 2025, Investment Platform. All Rights Reserved.
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
    );
};

export default Layout;
