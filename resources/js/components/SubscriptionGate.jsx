import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import apiService from '../services/apiService';

const SubscriptionGate = ({ 
    children, 
    requiredPlan = 'basic', 
    feature = null,
    fallback = null,
    showUpgradePrompt = true 
}) => {
    const [subscription, setSubscription] = useState(null);
    const [loading, setLoading] = useState(true);
    const [hasAccess, setHasAccess] = useState(false);

    useEffect(() => {
        checkSubscriptionAccess();
    }, [requiredPlan, feature]);

    const checkSubscriptionAccess = async () => {
        try {
            setLoading(true);
            const response = await apiService.get('/subscriptions');
            
            if (response.data.data && response.data.data.length > 0) {
                const activeSubscription = response.data.data.find(sub => 
                    sub.status === 'active' || sub.status === 'trialing'
                );
                
                if (activeSubscription) {
                    setSubscription(activeSubscription);
                    setHasAccess(checkAccess(activeSubscription));
                } else {
                    setHasAccess(false);
                }
            } else {
                setHasAccess(false);
            }
        } catch (err) {
            console.error('Error checking subscription access:', err);
            setHasAccess(false);
        } finally {
            setLoading(false);
        }
    };

    const checkAccess = (subscription) => {
        if (!subscription || !subscription.subscription_product) {
            return false;
        }

        const planName = subscription.subscription_product.name.toLowerCase();
        
        // Plan hierarchy: basic < premium < enterprise
        const planLevels = {
            'basic': 1,
            'premium': 2,
            'enterprise': 3
        };

        const userPlanLevel = planLevels[planName] || 0;
        const requiredPlanLevel = planLevels[requiredPlan.toLowerCase()] || 1;

        // Check plan level access
        if (userPlanLevel < requiredPlanLevel) {
            return false;
        }

        // Check specific feature access if specified
        if (feature && subscription.subscription_product.features) {
            const hasFeature = subscription.subscription_product.features.some(f => 
                f.toLowerCase().includes(feature.toLowerCase())
            );
            return hasFeature;
        }

        return true;
    };

    const getRequiredPlanName = () => {
        return requiredPlan.charAt(0).toUpperCase() + requiredPlan.slice(1);
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    if (hasAccess) {
        return children;
    }

    // Custom fallback component
    if (fallback) {
        return fallback;
    }

    // Default upgrade prompt
    if (!showUpgradePrompt) {
        return null;
    }

    return (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-700 border border-blue-200 dark:border-slate-600 rounded-lg p-6 text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 mb-4">
                <svg
                    className="h-6 w-6 text-blue-600 dark:text-blue-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                </svg>
            </div>
            
            <h3 className="text-lg font-medium text-slate-900 dark:text-white mb-2">
                {subscription ? 'Upgrade Required' : 'Subscription Required'}
            </h3>
            
            <p className="text-sm text-slate-600 dark:text-slate-400 mb-4">
                {subscription ? (
                    <>
                        This feature requires a <strong>{getRequiredPlanName()}</strong> plan or higher.
                        {feature && (
                            <> You need access to <strong>{feature}</strong> functionality.</>
                        )}
                    </>
                ) : (
                    <>
                        You need an active subscription to access this feature.
                        {feature && (
                            <> This feature requires <strong>{feature}</strong> functionality.</>
                        )}
                    </>
                )}
            </p>

            {subscription && (
                <div className="bg-white dark:bg-slate-800 rounded-md p-3 mb-4 text-left">
                    <div className="text-sm">
                        <div className="flex justify-between items-center">
                            <span className="text-slate-600 dark:text-slate-400">Current Plan:</span>
                            <span className="font-medium text-slate-900 dark:text-white">
                                {subscription.subscription_product.name}
                            </span>
                        </div>
                        <div className="flex justify-between items-center mt-1">
                            <span className="text-slate-600 dark:text-slate-400">Required:</span>
                            <span className="font-medium text-blue-600 dark:text-blue-400">
                                {getRequiredPlanName()} or higher
                            </span>
                        </div>
                    </div>
                </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link
                    to="/app/subscription/plans"
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    {subscription ? 'Upgrade Plan' : 'View Plans'}
                </Link>
                
                {subscription && (
                    <Link
                        to="/app/subscription/manage"
                        className="inline-flex items-center justify-center px-4 py-2 border border-slate-300 dark:border-slate-600 text-sm font-medium rounded-md text-slate-700 dark:text-slate-300 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        Manage Subscription
                    </Link>
                )}
            </div>
        </div>
    );
};

export default SubscriptionGate;
