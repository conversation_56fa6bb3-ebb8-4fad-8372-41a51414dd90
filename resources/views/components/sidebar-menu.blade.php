<!-- BEGIN: Sidebar -->
<div class="sidebar-wrapper group w-0 hidden xl:w-[248px] xl:block">
    <div id="bodyOverlay" class="w-screen h-screen fixed top-0 bg-slate-900 bg-opacity-50 backdrop-blur-sm z-10 hidden">
    </div>
    <div class="logo-segment">

        <!-- Application Logo -->
        <x-application-logo />

        <!-- Sidebar Type Button -->
        <div id="sidebar_type" class="cursor-pointer text-slate-900 dark:text-white text-lg">
            <iconify-icon class="sidebarDotIcon extend-icon text-slate-900 dark:text-slate-200" icon="fa-regular:dot-circle"></iconify-icon>
            <iconify-icon class="sidebarDotIcon collapsed-icon text-slate-900 dark:text-slate-200" icon="material-symbols:circle-outline"></iconify-icon>
        </div>
        <button class="sidebarCloseIcon text-2xl inline-block md:hidden">
            <iconify-icon class="text-slate-900 dark:text-slate-200" icon="clarity:window-close-line"></iconify-icon>
        </button>
    </div>
    <div id="nav_shadow" class="nav_shadow h-[60px] absolute top-[80px] nav-shadow z-[1] w-full transition-all duration-200 pointer-events-none
      opacity-0"></div>
    <div class="sidebar-menus bg-white dark:bg-slate-800 py-2 px-4 h-[calc(100%-80px)] z-50" id="sidebar_menus">
        <ul class="sidebar-menu">
            <li class="sidebar-menu-title">{{ __('MENU') }}</li>

            <!-- Dashboard -->
            <li class="{{ (request()->is('admin/dashboard*')) ? 'active' : '' }}">
                <a href="{{ route('admin.dashboard') }}" class="navItem">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="heroicons-outline:home"></iconify-icon>
                        <span>{{ __('Dashboard') }}</span>
                    </span>
                </a>
            </li>
            <!-----Investor----->
            <li>
                <a href="{{ route('investors.index') }}" class="navItem {{ (request()->is('investors*')) || (request()->is('investors*')) || (request()->is('investors*')) || (request()->is('investors*')) || (request()->is('investors*')) ? 'active' : '' }}">
                    <span class="flex items-center">
                        <iconify-icon class=" nav-icon" icon="material-symbols:settings-outline"></iconify-icon>
                        <span>{{ __('Investor') }}</span>
                    </span>
                </a>
            </li>
            <!-- Investor -->

            <!-----Investor Request----->

            <!-- Admin Management -->
            <li class="sidebar-menu-title">{{ __('MANAGEMENT') }}</li>

            <!-- Investment Platform -->
            <li class="{{ (request()->is('admin/investment*')) ? 'active' : '' }}">
                <a href="javascript:void(0)" class="navItem">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="heroicons-outline:trending-up"></iconify-icon>
                        <span>{{ __('Investment Platform') }}</span>
                    </span>
                    <iconify-icon class="icon-arrow" icon="heroicons-outline:chevron-right"></iconify-icon>
                </a>
                <ul class="sidebar-submenu">
                    <li>
                        <a href="{{ route('admin.investment.index') }}" class="navItem {{ (request()->is('admin/investment') && !request()->is('admin/investment/*')) ? 'active' : '' }}">
                            {{ __('Overview') }}
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.investment.interest-requests') }}" class="navItem {{ (request()->is('admin/investment/interest-requests*')) ? 'active' : '' }}">
                            {{ __('Interest Requests') }}
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.investment.users') }}" class="navItem {{ (request()->is('admin/investment/users*')) ? 'active' : '' }}">
                            {{ __('Platform Users') }}
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('admin.investment.esg-analytics') }}" class="navItem {{ (request()->is('admin/investment/esg-analytics*')) ? 'active' : '' }}">
                            {{ __('ESG Analytics') }}
                        </a>
                    </li>
                </ul>
            </li>

            <!-- Users -->
            <li>
                <a href="{{ route('investor-request.index') }}" class="navItem {{ (request()->is('investor-request*'))  ? 'active' : '' }}">
                    <span class="flex items-center">
                        <iconify-icon class=" nav-icon" icon="material-symbols:settings-outline"></iconify-icon>
                        <span>{{ __('Investor Request') }}</span>
                    </span>
                </a>
            </li>
            <!-- Investor Request-->

            <!-----Startups Company----->
            <li>
                <a href="{{ route('startups.index') }}" class="navItem {{ (request()->is('startups*')) || (request()->is('startups*')) || (request()->is('startups*')) || (request()->is('startups*')) || (request()->is('startups*')) ? 'active' : '' }}">
                    <span class="flex items-center">
                        <iconify-icon class=" nav-icon" icon="material-symbols:settings-outline"></iconify-icon>
                        <span>{{ __('Startup') }}</span>
                    </span>
                </a>
            </li>
            <!-- Startups Company -->

            <!-----Startups Request----->
            <li>
                <a href="{{ route('startup-request.index') }}" class="navItem {{ (request()->is('startup-request*')) || (request()->is('startup-request*')) || (request()->is('startup-request*')) || (request()->is('startup-request*')) || (request()->is('startup-request*')) ? 'active' : '' }}">
                    <span class="flex items-center">
                        <iconify-icon class=" nav-icon" icon="material-symbols:settings-outline"></iconify-icon>
                        <span>{{ __('Startup Request') }}</span>
                    </span>
                    <iconify-icon class="icon-arrow" icon="heroicons-outline:chevron-right"></iconify-icon>
                </a>
            </li>
            <!-- Startups Request -->

            <li>
                <a href="{{ route('general-settings.show') }}" class="navItem {{ (request()->is('general-settings*')) || (request()->is('users*')) || (request()->is('roles*')) || (request()->is('profiles*')) || (request()->is('permissions*')) ? 'active' : '' }}">
                    <span class="flex items-center">
                        <iconify-icon class="nav-icon" icon="material-symbols:settings-outline"></iconify-icon>
                        <span>{{ __('Settings') }}</span>
                    </span>
                </a>
            </li>
        </ul>
        <!-- Upgrade Your Business Plan Card Start -->
        <div class="bg-slate-900 mb-10 mt-24 p-4 relative text-center rounded-2xl text-white" id="sidebar_bottom_wizard">
            <img src="{{ asset('images/svg/rabit.svg') }}" alt="" class="mx-auto relative -mt-[73px]">
        </div>
        <!-- Upgrade Your Business Plan Card Start -->
    </div>
</div>
<!-- End: Sidebar -->
