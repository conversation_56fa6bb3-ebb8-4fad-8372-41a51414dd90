@extends('layouts.app')

@section('content')
<div class="space-y-8">
    <!-- <PERSON> Header -->
    <div class="md:flex md:items-center md:justify-between">
        <div class="min-w-0 flex-1">
            <h2 class="text-2xl font-bold leading-7 text-slate-900 dark:text-white sm:truncate sm:text-3xl sm:tracking-tight">
                ESG Analytics
            </h2>
            <p class="mt-1 text-sm text-slate-500 dark:text-slate-400">
                Environmental, Social, and Governance scoring analytics
            </p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Completed Assessments</dt>
                            <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ $stats['totalCompleted'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Average Score</dt>
                            <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ number_format($stats['averageScore'], 1) }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Excellent Scores</dt>
                            <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ $stats['scoreDistribution']['excellent'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-slate-800 overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-slate-500 dark:text-slate-400 truncate">Needs Improvement</dt>
                            <dd class="text-lg font-medium text-slate-900 dark:text-white">{{ $stats['scoreDistribution']['poor'] + $stats['scoreDistribution']['fair'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Score Distribution Chart -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
            <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
                <h3 class="text-lg font-medium text-slate-900 dark:text-white">Score Distribution</h3>
            </div>
            <div class="p-6">
                <canvas id="scoreDistributionChart" width="400" height="300"></canvas>
            </div>
        </div>

        <!-- Monthly Trend Chart -->
        <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
            <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
                <h3 class="text-lg font-medium text-slate-900 dark:text-white">Monthly ESG Completions</h3>
            </div>
            <div class="p-6">
                <canvas id="monthlyTrendChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Detailed Score Breakdown -->
    <div class="bg-white dark:bg-slate-800 shadow rounded-lg">
        <div class="px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <h3 class="text-lg font-medium text-slate-900 dark:text-white">Score Breakdown</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Excellent (80-100) -->
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-3">
                        <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-900 dark:text-white">Excellent</h4>
                    <p class="text-sm text-slate-500 dark:text-slate-400 mb-2">80-100 points</p>
                    <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $stats['scoreDistribution']['excellent'] }}</p>
                    <p class="text-xs text-slate-500 dark:text-slate-400">
                        {{ $stats['totalCompleted'] > 0 ? round(($stats['scoreDistribution']['excellent'] / $stats['totalCompleted']) * 100, 1) : 0 }}% of total
                    </p>
                </div>

                <!-- Good (60-79) -->
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mb-3">
                        <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-900 dark:text-white">Good</h4>
                    <p class="text-sm text-slate-500 dark:text-slate-400 mb-2">60-79 points</p>
                    <p class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $stats['scoreDistribution']['good'] }}</p>
                    <p class="text-xs text-slate-500 dark:text-slate-400">
                        {{ $stats['totalCompleted'] > 0 ? round(($stats['scoreDistribution']['good'] / $stats['totalCompleted']) * 100, 1) : 0 }}% of total
                    </p>
                </div>

                <!-- Fair (40-59) -->
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mb-3">
                        <svg class="w-8 h-8 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-900 dark:text-white">Fair</h4>
                    <p class="text-sm text-slate-500 dark:text-slate-400 mb-2">40-59 points</p>
                    <p class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ $stats['scoreDistribution']['fair'] }}</p>
                    <p class="text-xs text-slate-500 dark:text-slate-400">
                        {{ $stats['totalCompleted'] > 0 ? round(($stats['scoreDistribution']['fair'] / $stats['totalCompleted']) * 100, 1) : 0 }}% of total
                    </p>
                </div>

                <!-- Poor (0-39) -->
                <div class="text-center">
                    <div class="w-16 h-16 mx-auto bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-3">
                        <svg class="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-900 dark:text-white">Poor</h4>
                    <p class="text-sm text-slate-500 dark:text-slate-400 mb-2">0-39 points</p>
                    <p class="text-2xl font-bold text-red-600 dark:text-red-400">{{ $stats['scoreDistribution']['poor'] }}</p>
                    <p class="text-xs text-slate-500 dark:text-slate-400">
                        {{ $stats['totalCompleted'] > 0 ? round(($stats['scoreDistribution']['poor'] / $stats['totalCompleted']) * 100, 1) : 0 }}% of total
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Score Distribution Pie Chart
    const scoreCtx = document.getElementById('scoreDistributionChart').getContext('2d');
    new Chart(scoreCtx, {
        type: 'doughnut',
        data: {
            labels: ['Excellent (80-100)', 'Good (60-79)', 'Fair (40-59)', 'Poor (0-39)'],
            datasets: [{
                data: [
                    {{ $stats['scoreDistribution']['excellent'] }},
                    {{ $stats['scoreDistribution']['good'] }},
                    {{ $stats['scoreDistribution']['fair'] }},
                    {{ $stats['scoreDistribution']['poor'] }}
                ],
                backgroundColor: [
                    '#10B981', // Green
                    '#3B82F6', // Blue
                    '#F59E0B', // Yellow
                    '#EF4444'  // Red
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Monthly Trend Line Chart
    const trendCtx = document.getElementById('monthlyTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: @json($chartData['labels']),
            datasets: [{
                label: 'ESG Completions',
                data: @json($chartData['completions']),
                borderColor: '#3B82F6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
});
</script>
@endsection
