<x-app-layout>
    <div class="space-y-8">
        <div class="block sm:flex items-center justify-between mb-6">
            <x-breadcrumb :pageTitle="$pageTitle"/>

            <!-- BEGIN: Breadcrumb -->
            <div class="mb-5">
                <ul class="m-0 p-0 list-none">
                    <li class="inline-block relative top-[3px] text-base text-primary-500 font-Inter ">
                        <a href="{{ route('dashboard.index') }}">
                            <iconify-icon icon="heroicons-outline:home"></iconify-icon>
                            <iconify-icon icon="heroicons-outline:chevron-right"
                                          class="relative text-slate-500 text-sm rtl:rotate-180"></iconify-icon>
                        </a>
                    </li>
                    <li class="inline-block relative text-sm text-primary-500 font-Inter ">
                        Investor Request
                        <iconify-icon icon="heroicons-outline:chevron-right"
                                      class="relative top-[3px] text-slate-500 rtl:rotate-180"></iconify-icon>
                    </li>

                </ul>
            </div>
            <!-- END: BreadCrumb -->

        </div>

    </div>

    <div class="card">
        <header class=" card-header noborder">
            <h4 class="card-title">All Request
            </h4>
        </header>
        <div class="card-body px-6 pb-6">
            <div class="overflow-x-auto -mx-6 dashcode-data-table">
                <span class=" col-span-8  hidden"></span>
                <span class="  col-span-4 hidden"></span>
                <div class="inline-block min-w-full align-middle">
                    <div class="overflow-hidden ">
                        <table class="min-w-full divide-y divide-slate-100 table-fixed dark:divide-slate-700"
                               id="data-table">
                            <thead class=" border-t border-slate-100 dark:border-slate-800">
                            <tr>
                                <th scope="col" class=" table-th ">SL</th>

                                <th scope="col" class=" table-th ">Investor Name</th>

                                <th scope="col" class=" table-th "> Startup Company Name</th>

                                <th scope="col" class=" table-th "> Request Date & Time</th>

                                <th scope="col" class=" table-th ">Status</th>

                                <th scope="col" class=" table-th ">Action</th>

                            </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-slate-100 dark:bg-slate-800 dark:divide-slate-700">

                            <tr>
                                <td class="table-td">1</td>
                                <td class="table-td">
                                    <span class="flex">
                                    <span class="w-7 h-7 rounded-full ltr:mr-3 rtl:ml-3 flex-none">
                                        <img src="{{ asset('images/all-img/customer_1.png') }}" alt="1"
                                        class="object-cover w-full h-full rounded-full">
                                    </span>
                                    <span
                                        class="text-sm text-slate-600 dark:text-slate-300 capitalize">Investor Company</span>
                                    </span>
                                </td>
                                <td class="table-td">
                                    <span class="flex">
                                  <span class="w-7 h-7 rounded-full ltr:mr-3 rtl:ml-3 flex-none">
                                    <img src="{{ asset('images/all-img/customer_1.png') }}" alt="1"
                                         class="object-cover w-full h-full rounded-full">
                                  </span>
                                    <span
                                        class="text-sm text-slate-600 dark:text-slate-300 capitalize">Startup Company</span>
                                    </span>
                                </td>
                                <td class="table-td ">3/26/2022</td>

                                <td class="table-td ">

                                    <div class="inline-block px-3 min-w-[90px] text-center mx-auto py-1 rounded-[999px] bg-opacity-25 text-warning-500
                                        bg-warning-500">
                                        Pending
                                    </div>

                                </td>
                                <td class="table-td ">
                                    <div>
                                        <div class="relative">
                                            <div class="dropdown relative">
                                                <button class="text-xl text-center block w-full " type="button"
                                                        id="tableDropdownMenuButton1" data-bs-toggle="dropdown"
                                                        aria-expanded="false">
                                                    <iconify-icon icon="heroicons-outline:dots-vertical"></iconify-icon>
                                                </button>
                                                <ul class=" dropdown-menu min-w-[120px] absolute text-sm text-slate-700 dark:text-white hidden bg-white dark:bg-slate-700
                                  shadow z-[2] float-left overflow-hidden list-none text-left rounded-lg mt-1 m-0 bg-clip-padding border-none">
                                                    <li>
                                                        <a href="#" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                      dark:hover:text-white">
                                                            View</a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                      dark:hover:text-white">
                                                            Edit</a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="text-slate-600 dark:text-white block font-Inter font-normal px-4 py-2 hover:bg-slate-100 dark:hover:bg-slate-600
                                      dark:hover:text-white">
                                                            Delete</a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @vite(['resources/js/plugins-old/jquery-3.6.0.min.js'])
    @push('scripts')
        <script type="module">

        </script>
    @endpush
</x-app-layout>
