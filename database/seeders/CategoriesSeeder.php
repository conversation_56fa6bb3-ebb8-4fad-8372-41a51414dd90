<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            // Technology & Software
            [
                'name' => 'Artificial Intelligence & Machine Learning',
                'description' => 'AI/ML technologies, automation, and intelligent systems',
                'type' => 'both',
                'sort_order' => 1,
            ],
            [
                'name' => 'Software as a Service (SaaS)',
                'description' => 'Cloud-based software solutions and platforms',
                'type' => 'both',
                'sort_order' => 2,
            ],
            [
                'name' => 'Cybersecurity',
                'description' => 'Information security, data protection, and privacy solutions',
                'type' => 'both',
                'sort_order' => 3,
            ],
            [
                'name' => 'Blockchain & Cryptocurrency',
                'description' => 'Distributed ledger technologies and digital currencies',
                'type' => 'both',
                'sort_order' => 4,
            ],
            [
                'name' => 'Internet of Things (IoT)',
                'description' => 'Connected devices and smart systems',
                'type' => 'both',
                'sort_order' => 5,
            ],

            // Healthcare & Life Sciences
            [
                'name' => 'Digital Health & Telemedicine',
                'description' => 'Remote healthcare delivery and digital health solutions',
                'type' => 'both',
                'sort_order' => 6,
            ],
            [
                'name' => 'Biotechnology',
                'description' => 'Biological research and pharmaceutical development',
                'type' => 'both',
                'sort_order' => 7,
            ],
            [
                'name' => 'Medical Devices',
                'description' => 'Healthcare equipment and diagnostic tools',
                'type' => 'both',
                'sort_order' => 8,
            ],

            // Financial Services
            [
                'name' => 'Fintech',
                'description' => 'Financial technology and digital banking solutions',
                'type' => 'both',
                'sort_order' => 9,
            ],
            [
                'name' => 'Insurance Technology (Insurtech)',
                'description' => 'Digital insurance solutions and risk management',
                'type' => 'both',
                'sort_order' => 10,
            ],

            // Sustainability & Environment
            [
                'name' => 'Clean Energy & Renewable Technology',
                'description' => 'Solar, wind, and other renewable energy solutions',
                'type' => 'both',
                'sort_order' => 11,
            ],
            [
                'name' => 'Environmental Technology (Cleantech)',
                'description' => 'Environmental monitoring and pollution control',
                'type' => 'both',
                'sort_order' => 12,
            ],
            [
                'name' => 'Sustainable Agriculture (AgTech)',
                'description' => 'Agricultural technology and sustainable farming',
                'type' => 'both',
                'sort_order' => 13,
            ],

            // Consumer & Retail
            [
                'name' => 'E-commerce & Marketplace',
                'description' => 'Online retail platforms and digital marketplaces',
                'type' => 'both',
                'sort_order' => 14,
            ],
            [
                'name' => 'Consumer Products & Services',
                'description' => 'Direct-to-consumer brands and retail innovations',
                'type' => 'both',
                'sort_order' => 15,
            ],

            // Education & Training
            [
                'name' => 'Education Technology (EdTech)',
                'description' => 'Digital learning platforms and educational tools',
                'type' => 'both',
                'sort_order' => 16,
            ],

            // Transportation & Logistics
            [
                'name' => 'Transportation & Mobility',
                'description' => 'Transportation solutions and mobility services',
                'type' => 'both',
                'sort_order' => 17,
            ],
            [
                'name' => 'Supply Chain & Logistics',
                'description' => 'Logistics optimization and supply chain management',
                'type' => 'both',
                'sort_order' => 18,
            ],

            // Real Estate & Construction
            [
                'name' => 'Property Technology (PropTech)',
                'description' => 'Real estate technology and property management',
                'type' => 'both',
                'sort_order' => 19,
            ],
            [
                'name' => 'Construction Technology (ConTech)',
                'description' => 'Construction automation and building technologies',
                'type' => 'both',
                'sort_order' => 20,
            ],

            // Media & Entertainment
            [
                'name' => 'Media & Entertainment Technology',
                'description' => 'Digital content, gaming, and entertainment platforms',
                'type' => 'both',
                'sort_order' => 21,
            ],

            // Social Impact
            [
                'name' => 'Social Impact & Non-Profit',
                'description' => 'Social enterprises and impact-driven organizations',
                'type' => 'both',
                'sort_order' => 22,
            ],
        ];

        foreach ($categories as $category) {
            DB::table('categories')->insert([
                'name' => $category['name'],
                'description' => $category['description'],
                'type' => $category['type'],
                'sort_order' => $category['sort_order'],
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
