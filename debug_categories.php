<?php

require_once 'vendor/autoload.php';

use App\Models\User;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Debugging Categories...\n\n";

$investor = User::where('email', '<EMAIL>')->first();
$startup = User::where('email', '<EMAIL>')->first();

if (!$investor || !$startup) {
    echo "Users not found.\n";
    exit(1);
}

echo "Investor Categories:\n";
$investorCategories = $investor->categories;
foreach ($investorCategories as $cat) {
    echo "- ID: " . $cat->id . ", Name: " . $cat->name . "\n";
}

echo "\nStartup Categories:\n";
$startupCategories = $startup->categories;
foreach ($startupCategories as $cat) {
    echo "- ID: " . $cat->id . ", Name: " . $cat->name . "\n";
}

echo "\nMatching Categories:\n";
$investorCategoryIds = $investorCategories->pluck('id')->toArray();
$startupCategoryIds = $startupCategories->pluck('id')->toArray();
$matchingIds = array_intersect($investorCategoryIds, $startupCategoryIds);

if (empty($matchingIds)) {
    echo "No matching categories found!\n";
} else {
    echo "Matching category IDs: " . implode(', ', $matchingIds) . "\n";
}

echo "\nDone.\n";
