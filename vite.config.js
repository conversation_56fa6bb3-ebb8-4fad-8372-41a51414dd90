import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import react from "@vitejs/plugin-react";

export default defineConfig({
    plugins: [
        react(),
        laravel({
            input: [
                "resources/css/app.scss",
                "resources/js/custom/store.js",
                "resources/js/main.js",
                "resources/js/app.js",
                "resources/js/plugins-old/jquery-3.6.0.min.js",
                "resources/js/react-app.jsx", // New React entry point
            ],
            refresh: true,
        }),
    ],
});
