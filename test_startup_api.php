<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Category;
use Illuminate\Support\Facades\Hash;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing Startup API endpoints...\n\n";

// Create test startup user
$startupUser = User::where('email', '<EMAIL>')->first();
if (!$startupUser) {
    $startupUser = User::create([
        'name' => 'Test Startup',
        'email' => '<EMAIL>',
        'password' => Hash::make('password'),
        'email_verified_at' => now(),
    ]);
    $startupUser->assignRole('startup');
    // Also assign sanctum guard role for API access
    $startupUser->assignRole(\Spatie\Permission\Models\Role::where(['name' => 'startup', 'guard_name' => 'sanctum'])->first());
} else {
    // Ensure user has the startup role for both guards
    if (!$startupUser->hasRole('startup')) {
        $startupUser->assignRole('startup');
    }
    // Ensure sanctum guard role
    $sanctumRole = \Spatie\Permission\Models\Role::where(['name' => 'startup', 'guard_name' => 'sanctum'])->first();
    if ($sanctumRole && !$startupUser->hasRole($sanctumRole)) {
        $startupUser->assignRole($sanctumRole);
    }
}

// Create API token
$token = $startupUser->createToken('test-token')->plainTextToken;

echo "Created startup user with token: " . substr($token, 0, 20) . "...\n";
echo "User sanctum roles: " . implode(', ', $startupUser->getRoleNames('sanctum')->toArray()) . "\n\n";

// Test Startup Profile Creation
echo "Testing Startup Profile Creation...\n";
$profileData = [
    'company_name' => 'Test Startup Inc',
    'company_description' => 'A test startup for API testing focused on innovative technology solutions',
    'founding_date' => '2023-01-01',
    'employee_count' => 5,
    'website' => 'https://teststartup.com',
    'linkedin' => 'https://linkedin.com/company/teststartup',
    'funding_stage' => 'seed',
    'funding_amount_sought' => 500000,
    'current_valuation' => 2000000,
    'business_model' => ['SaaS', 'B2B'],
    'categories' => Category::take(2)->pluck('id')->toArray(),
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/startup-profiles');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($profileData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 300) . "...\n\n";

// Test ESG Response Submission
echo "Testing ESG Response Submission...\n";
$esgData = [
    'responses' => [
        ['question_id' => 1, 'response_value' => 'Yes'],
        ['question_id' => 2, 'response_value' => '8'],
        ['question_id' => 3, 'response_value' => 'Yes'],
    ]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/esg/responses');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($esgData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 300) . "...\n\n";

// Test Discovery of Investors
echo "Testing Discovery of Investors...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/discovery/investors');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 200) . "...\n\n";

// Cleanup
$startupUser->tokens()->delete();

echo "Test completed! Cleaned up tokens.\n";
