<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\StartupProfile;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Debugging Startup Profile...\n\n";

$startup = User::where('email', '<EMAIL>')->first();
if (!$startup) {
    echo "Startup user not found.\n";
    exit(1);
}

echo "Startup User ID: " . $startup->id . "\n";
echo "Startup Roles: " . implode(', ', $startup->getRoleNames()->toArray()) . "\n\n";

$profile = $startup->startupProfile;
if (!$profile) {
    echo "Startup profile not found.\n";
    exit(1);
}

echo "Startup Profile Details:\n";
echo "ID: " . $profile->id . "\n";
echo "Company Name: " . $profile->company_name . "\n";
echo "Profile Completed: " . ($profile->profile_completed ? 'Yes' : 'No') . "\n";
echo "ESG Completed: " . ($profile->esg_completed ? 'Yes' : 'No') . "\n";
echo "ESG Score: " . $profile->esg_score . "\n";
echo "Founding Date: " . $profile->founding_date . "\n";
echo "Employee Count: " . $profile->employee_count . "\n";
echo "Funding Stage: " . $profile->funding_stage . "\n";
echo "Funding Amount Sought: " . $profile->funding_amount_sought . "\n\n";

// Check what the completed() scope returns
echo "Checking completed() scope...\n";
$completedProfiles = StartupProfile::completed()->get();
echo "Total completed profiles: " . $completedProfiles->count() . "\n";

foreach ($completedProfiles as $cp) {
    echo "- Profile ID: " . $cp->id . ", User: " . $cp->user->name . ", Completed: " . ($cp->profile_completed ? 'Yes' : 'No') . "\n";
}

echo "\nDone.\n";
