<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class InvestorProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'investment_budget_min',
        'investment_budget_max',
        'risk_tolerance',
        'investment_experience',
        'bio',
        'website',
        'linkedin',
        'investment_preferences',
        'profile_completed',
    ];

    protected $casts = [
        'investment_budget_min' => 'decimal:2',
        'investment_budget_max' => 'decimal:2',
        'investment_preferences' => 'array',
        'profile_completed' => 'boolean',
    ];

    /**
     * Get the user that owns the investor profile
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get interest requests sent by this investor
     */
    public function sentInterestRequests(): HasMany
    {
        return $this->hasMany(InterestRequest::class, 'requester_id', 'user_id');
    }

    /**
     * Get interest requests received by this investor
     */
    public function receivedInterestRequests(): HasMany
    {
        return $this->hasMany(InterestRequest::class, 'target_id', 'user_id');
    }

    /**
     * Scope for completed profiles
     */
    public function scopeCompleted($query)
    {
        return $query->where('profile_completed', true);
    }

    /**
     * Scope for profiles by risk tolerance
     */
    public function scopeByRiskTolerance($query, $riskTolerance)
    {
        return $query->where('risk_tolerance', $riskTolerance);
    }

    /**
     * Scope for profiles by investment experience
     */
    public function scopeByExperience($query, $experience)
    {
        return $query->where('investment_experience', $experience);
    }

    /**
     * Scope for profiles within budget range
     */
    public function scopeWithinBudget($query, $minAmount, $maxAmount = null)
    {
        $query->where(function ($q) use ($minAmount, $maxAmount) {
            $q->where('investment_budget_min', '<=', $maxAmount ?? $minAmount)
              ->where('investment_budget_max', '>=', $minAmount);
        });

        return $query;
    }
}
