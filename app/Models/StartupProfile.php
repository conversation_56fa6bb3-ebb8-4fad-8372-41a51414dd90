<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StartupProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'company_name',
        'company_description',
        'founding_date',
        'employee_count',
        'website',
        'linkedin',
        'funding_stage',
        'funding_amount_sought',
        'current_valuation',
        'business_model',
        'esg_score',
        'esg_breakdown',
        'esg_completed',
        'profile_completed',
    ];

    protected $casts = [
        'founding_date' => 'date',
        'employee_count' => 'integer',
        'funding_amount_sought' => 'decimal:2',
        'current_valuation' => 'decimal:2',
        'business_model' => 'array',
        'esg_score' => 'decimal:2',
        'esg_breakdown' => 'array',
        'esg_completed' => 'boolean',
        'profile_completed' => 'boolean',
    ];

    /**
     * Get the user that owns the startup profile
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get ESG responses for this startup
     */
    public function esgResponses(): HasMany
    {
        return $this->hasMany(EsgResponse::class);
    }

    /**
     * Get interest requests sent by this startup
     */
    public function sentInterestRequests(): HasMany
    {
        return $this->hasMany(InterestRequest::class, 'requester_id', 'user_id');
    }

    /**
     * Get interest requests received by this startup
     */
    public function receivedInterestRequests(): HasMany
    {
        return $this->hasMany(InterestRequest::class, 'target_id', 'user_id');
    }

    /**
     * Scope for completed profiles
     */
    public function scopeCompleted($query)
    {
        return $query->where('profile_completed', true);
    }

    /**
     * Scope for profiles with completed ESG
     */
    public function scopeEsgCompleted($query)
    {
        return $query->where('esg_completed', true);
    }

    /**
     * Scope for profiles by funding stage
     */
    public function scopeByFundingStage($query, $stage)
    {
        return $query->where('funding_stage', $stage);
    }

    /**
     * Scope for profiles by ESG score range
     */
    public function scopeByEsgScore($query, $minScore, $maxScore = null)
    {
        $query->where('esg_score', '>=', $minScore);

        if ($maxScore !== null) {
            $query->where('esg_score', '<=', $maxScore);
        }

        return $query;
    }

    /**
     * Scope for profiles seeking funding within range
     */
    public function scopeSeekingFunding($query, $minAmount, $maxAmount = null)
    {
        $query->whereNotNull('funding_amount_sought')
              ->where('funding_amount_sought', '>=', $minAmount);

        if ($maxAmount !== null) {
            $query->where('funding_amount_sought', '<=', $maxAmount);
        }

        return $query;
    }

    /**
     * Calculate and update ESG score based on responses
     */
    public function calculateEsgScore(): void
    {
        $responses = $this->esgResponses()->with('esgQuestion')->get();

        if ($responses->isEmpty()) {
            return;
        }

        $totalScore = 0;
        $totalWeight = 0;
        $categoryScores = [
            'environmental' => ['score' => 0, 'weight' => 0],
            'social' => ['score' => 0, 'weight' => 0],
            'governance' => ['score' => 0, 'weight' => 0],
        ];

        foreach ($responses as $response) {
            $question = $response->esgQuestion;
            $score = $this->calculateResponseScore($response, $question);

            $totalScore += $score * $question->weight;
            $totalWeight += $question->weight;

            $categoryScores[$question->category]['score'] += $score * $question->weight;
            $categoryScores[$question->category]['weight'] += $question->weight;
        }

        // Calculate overall score (0-100)
        $overallScore = $totalWeight > 0 ? ($totalScore / $totalWeight) * 20 : 0; // Scale to 100

        // Calculate category scores
        $breakdown = [];
        foreach ($categoryScores as $category => $data) {
            $breakdown[$category] = $data['weight'] > 0 ?
                ($data['score'] / $data['weight']) * 20 : 0; // Scale to 100
        }

        $this->update([
            'esg_score' => round($overallScore, 2),
            'esg_breakdown' => $breakdown,
        ]);
    }

    /**
     * Calculate score for individual response
     */
    private function calculateResponseScore($response, $question): float
    {
        switch ($question->type) {
            case 'yes_no':
                return $response->response_value === 'Yes' ? 5 : 1;

            case 'scale':
                // Extract numeric value from scale response
                preg_match('/(\d+)/', $response->response_value, $matches);
                return isset($matches[1]) ? (float) $matches[1] : 1;

            case 'multiple_choice':
                // Score based on position in options array
                $options = $question->options;
                $index = array_search($response->response_value, $options);
                return $index !== false ? ($index + 1) : 1;

            default:
                return 3; // Default middle score for text responses
        }
    }
}
