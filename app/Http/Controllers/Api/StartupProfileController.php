<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\StartupProfile;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class StartupProfileController extends Controller
{
    public function __construct()
    {
        // Authentication is handled by route middleware
        // Role-based authorization is handled by route middleware
    }

    /**
     * Display a listing of startup profiles (for analysts and investors)
     */
    public function index(Request $request): JsonResponse
    {
        $query = StartupProfile::with(['user', 'user.categories'])
            ->completed();

        // Apply filters
        if ($request->has('funding_stage')) {
            $query->byFundingStage($request->funding_stage);
        }

        if ($request->has('min_esg_score')) {
            $query->byEsgScore($request->min_esg_score, $request->max_esg_score);
        }

        if ($request->has('min_funding') || $request->has('max_funding')) {
            $query->seekingFunding(
                $request->min_funding ?? 0,
                $request->max_funding
            );
        }

        if ($request->has('categories')) {
            $categoryIds = is_array($request->categories)
                ? $request->categories
                : explode(',', $request->categories);

            $query->whereHas('user.categories', function ($q) use ($categoryIds) {
                $q->whereIn('categories.id', $categoryIds);
            });
        }

        $profiles = $query->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => $profiles,
        ]);
    }

    /**
     * Store or update startup profile
     */
    public function store(Request $request): JsonResponse
    {
        $user = auth()->user();

        if (!$user->isStartup()) {
            return response()->json([
                'success' => false,
                'message' => 'Only startups can create startup profiles',
            ], 403);
        }

        $validated = $request->validate([
            'company_name' => 'required|string|max:255',
            'company_description' => 'required|string|max:2000',
            'founding_date' => 'nullable|date|before_or_equal:today',
            'employee_count' => 'nullable|integer|min:1',
            'website' => 'nullable|url|max:255',
            'linkedin' => 'nullable|url|max:255',
            'funding_stage' => ['nullable', Rule::in(['pre_seed', 'seed', 'series_a', 'series_b', 'series_c', 'later_stage'])],
            'funding_amount_sought' => 'nullable|numeric|min:0',
            'current_valuation' => 'nullable|numeric|min:0',
            'business_model' => 'nullable|array',
            'categories' => 'nullable|array',
            'categories.*' => 'exists:categories,id',
        ]);

        // Remove categories from profile data as it's handled separately
        $profileData = $validated;
        unset($profileData['categories']);

        $profile = StartupProfile::updateOrCreate(
            ['user_id' => $user->id],
            array_merge($profileData, [
                'profile_completed' => $this->isProfileComplete($profileData),
            ])
        );

        // Sync categories if provided
        if (isset($validated['categories'])) {
            $user->categories()->sync($validated['categories']);
        }

        return response()->json([
            'success' => true,
            'message' => 'Startup profile saved successfully',
            'data' => $profile->load(['user', 'user.categories']),
        ]);
    }

    /**
     * Display the current user's startup profile
     */
    public function show(): JsonResponse
    {
        $user = auth()->user();
        $profile = $user->startupProfile()->with(['user', 'user.categories'])->first();

        if (!$profile) {
            return response()->json([
                'success' => false,
                'message' => 'Startup profile not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $profile,
        ]);
    }

    /**
     * Update startup profile
     */
    public function update(Request $request): JsonResponse
    {
        return $this->store($request);
    }

    /**
     * Remove startup profile
     */
    public function destroy(): JsonResponse
    {
        $user = auth()->user();
        $profile = $user->startupProfile;

        if (!$profile) {
            return response()->json([
                'success' => false,
                'message' => 'Startup profile not found',
            ], 404);
        }

        // Delete ESG responses
        $profile->esgResponses()->delete();
        $profile->delete();
        $user->categories()->detach();

        return response()->json([
            'success' => true,
            'message' => 'Startup profile deleted successfully',
        ]);
    }

    /**
     * Check if profile is complete
     */
    private function isProfileComplete(array $data): bool
    {
        $requiredFields = [
            'company_name',
            'company_description',
            'founding_date',
            'employee_count',
            'funding_stage',
            'funding_amount_sought',
        ];

        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                return false;
            }
        }

        return true;
    }
}
