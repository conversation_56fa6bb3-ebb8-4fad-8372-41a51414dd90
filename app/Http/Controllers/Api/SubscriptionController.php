<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Subscription\SubscribeRequest;
use App\Http\Requests\Api\Subscription\UpdateSubscriptionRequest;
use App\Models\SubscriptionProduct;
use App\Models\UserSubscription;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
        $this->middleware(['auth:sanctum', 'check.account.locked']);
        $this->authorizeResource(UserSubscription::class, 'subscription');
    }

    /**
     * Display user's subscriptions.
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();
        $subscriptions = $user->subscriptions()
            ->with('subscriptionProduct')
            ->orderBy('created_at', 'desc')
            ->get();

        return $this->responseWithSuccess(
            'User subscriptions retrieved successfully',
            $subscriptions
        );
    }

    /**
     * Create subscription checkout session.
     */
    public function store(SubscribeRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $user = Auth::user();
        $product = SubscriptionProduct::findOrFail($validated['subscription_product_id']);

        try {
            // Check if user already has an active subscription
            if ($user->hasActiveSubscription()) {
                return $this->responseWithError(
                    'User already has an active subscription',
                    Response::HTTP_CONFLICT
                );
            }

            // Create or get Stripe customer
            $customer = $this->stripeService->createOrGetCustomer($user);

            // Create success and cancel URLs
            $successUrl = config('app.frontend_url', 'http://localhost:5176') . '/app/subscription/success?session_id={CHECKOUT_SESSION_ID}';
            $cancelUrl = config('app.frontend_url', 'http://localhost:5176') . '/app/subscription/plans?canceled=true';

            // Create subscription checkout session
            $session = $this->stripeService->createSubscriptionCheckoutSession(
                $customer->id,
                $product->stripe_price_id,
                $successUrl,
                $cancelUrl,
                [
                    'user_id' => $user->id,
                    'subscription_product_id' => $product->id,
                ]
            );

            return $this->responseWithSuccess(
                'Checkout session created successfully',
                [
                    'checkout_url' => $session->url,
                    'session_id' => $session->id,
                ],
                Response::HTTP_CREATED
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to create subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Handle successful subscription checkout.
     */
    public function handleCheckoutSuccess(Request $request): JsonResponse
    {
        try {
            $sessionId = $request->get('session_id');

            if (!$sessionId) {
                return $this->responseWithError(
                    'No session ID provided',
                    Response::HTTP_BAD_REQUEST
                );
            }

            // Retrieve the checkout session
            $session = $this->stripeService->retrieveCheckoutSession($sessionId);

            if (!$session->subscription) {
                return $this->responseWithError(
                    'No subscription found in session',
                    Response::HTTP_BAD_REQUEST
                );
            }

            $user = Auth::user();
            $stripeSubscription = $this->stripeService->getSubscription($session->subscription);

            // Find the subscription product
            $productId = $session->metadata['subscription_product_id'] ?? null;
            if (!$productId) {
                return $this->responseWithError(
                    'Product ID not found in session metadata',
                    Response::HTTP_BAD_REQUEST
                );
            }

            $product = SubscriptionProduct::findOrFail($productId);

            // Create local subscription record
            $subscription = UserSubscription::create([
                'user_id' => $user->id,
                'subscription_product_id' => $product->id,
                'stripe_subscription_id' => $stripeSubscription->id,
                'status' => $stripeSubscription->status,
                'current_period_start' => \Carbon\Carbon::createFromTimestamp($stripeSubscription->current_period_start),
                'current_period_end' => \Carbon\Carbon::createFromTimestamp($stripeSubscription->current_period_end),
                'amount' => $product->price,
                'currency' => 'usd',
            ]);

            return $this->responseWithSuccess(
                'Subscription created successfully',
                $subscription->load('subscriptionProduct')
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to process subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Display the specified subscription.
     */
    public function show(UserSubscription $subscription): JsonResponse
    {
        // Authorization is handled by the policy
        $subscription->load(['subscriptionProduct', 'payments', 'invoices']);

        return $this->responseWithSuccess(
            'Subscription retrieved successfully',
            $subscription
        );
    }

    /**
     * Update subscription (upgrade/downgrade).
     */
    public function update(UpdateSubscriptionRequest $request, UserSubscription $subscription): JsonResponse
    {
        // Authorization is handled by the policy
        $validated = $request->validated();

        $newProduct = SubscriptionProduct::findOrFail($validated['subscription_product_id']);

        try {
            // Update subscription in Stripe
            $stripeSubscription = $this->stripeService->updateSubscription(
                $subscription->stripe_subscription_id,
                $newProduct->stripe_price_id
            );

            // Update local subscription
            $subscription->update([
                'subscription_product_id' => $newProduct->id,
                'status' => $stripeSubscription->status,
                'amount' => $newProduct->price,
            ]);

            return $this->responseWithSuccess(
                'Subscription updated successfully',
                $subscription->fresh()->load('subscriptionProduct')
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to update subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Cancel subscription.
     */
    public function destroy(UserSubscription $subscription): JsonResponse
    {
        // Authorization is handled by the policy

        try {
            // Cancel subscription in Stripe
            $stripeSubscription = $this->stripeService->cancelSubscription(
                $subscription->stripe_subscription_id
            );

            // Update local subscription
            $subscription->update([
                'status' => 'canceled',
                'canceled_at' => now(),
                'ends_at' => \Carbon\Carbon::createFromTimestamp($stripeSubscription->current_period_end),
            ]);

            return $this->responseWithSuccess(
                'Subscription canceled successfully',
                $subscription->fresh()
            );

        } catch (\Exception $e) {
            return $this->responseWithError(
                'Failed to cancel subscription: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
