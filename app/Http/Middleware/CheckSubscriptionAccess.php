<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscriptionAccess
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $feature = null, string $tier = null): Response
    {
        $user = Auth::user();

        if (!$user) {
            return $this->handleUnauthenticated($request);
        }

        // Get user's active subscription
        $subscription = $user->activeSubscription;

        // Check if user has any active subscription
        if (!$subscription || !$subscription->isActive()) {
            return $this->handleNoActiveSubscription($request, $feature);
        }

        // If specific feature is required, check feature access
        if ($feature && !$this->hasFeatureAccess($subscription, $feature)) {
            return $this->handleInsufficientAccess($request, $feature);
        }

        // If specific tier is required, check tier access
        if ($tier && !$this->hasTierAccess($subscription, $tier)) {
            return $this->handleInsufficientTier($request, $tier);
        }

        // Check subscription limits for the current request
        if (!$this->checkSubscriptionLimits($subscription, $request)) {
            return $this->handleLimitExceeded($request);
        }

        return $next($request);
    }

    /**
     * Check if subscription has access to specific feature
     */
    protected function hasFeatureAccess($subscription, string $feature): bool
    {
        $limits = $subscription->subscriptionProduct->limits ?? [];
        
        return match($feature) {
            'advanced_analytics' => $limits['advanced_analytics'] ?? false,
            'priority_support' => $limits['priority_support'] ?? false,
            'custom_reports' => $limits['custom_reports'] ?? false,
            'api_access' => $limits['api_access'] ?? false,
            'dedicated_support' => $limits['dedicated_support'] ?? false,
            'priority_listing' => $limits['priority_listing'] ?? false,
            'featured_placement' => $limits['featured_placement'] ?? false,
            default => true, // Allow access to basic features by default
        };
    }

    /**
     * Check if subscription has access to specific tier
     */
    protected function hasTierAccess($subscription, string $tier): bool
    {
        $productName = $subscription->subscriptionProduct->name;
        
        return match($tier) {
            'basic' => str_contains(strtolower($productName), 'basic') || 
                      str_contains(strtolower($productName), 'premium') || 
                      str_contains(strtolower($productName), 'enterprise'),
            'premium' => str_contains(strtolower($productName), 'premium') || 
                        str_contains(strtolower($productName), 'enterprise'),
            'enterprise' => str_contains(strtolower($productName), 'enterprise'),
            default => true,
        };
    }

    /**
     * Check subscription limits for current request
     */
    protected function checkSubscriptionLimits($subscription, Request $request): bool
    {
        $limits = $subscription->subscriptionProduct->limits ?? [];
        $route = $request->route()->getName();

        // Check monthly limits based on route
        if (str_contains($route, 'discovery') || str_contains($route, 'startups')) {
            return $this->checkMonthlyLimit($subscription, 'startup_views_per_month');
        }

        if (str_contains($route, 'interest-request')) {
            return $this->checkMonthlyLimit($subscription, 'interest_requests_per_month');
        }

        if (str_contains($route, 'investor-inquiries')) {
            return $this->checkMonthlyLimit($subscription, 'investor_inquiries_per_month');
        }

        return true; // No limits apply to this route
    }

    /**
     * Check monthly usage limit
     */
    protected function checkMonthlyLimit($subscription, string $limitType): bool
    {
        $limits = $subscription->subscriptionProduct->limits ?? [];
        $monthlyLimit = $limits[$limitType] ?? 0;

        // -1 means unlimited
        if ($monthlyLimit === -1) {
            return true;
        }

        // If limit is 0, no access allowed
        if ($monthlyLimit === 0) {
            return false;
        }

        // TODO: Implement actual usage tracking
        // For now, we'll allow access (this should be implemented with usage tracking)
        return true;
    }

    /**
     * Handle unauthenticated user
     */
    protected function handleUnauthenticated(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Authentication required to access this feature.',
                'error_code' => 'UNAUTHENTICATED',
            ], 401);
        }

        return redirect()->route('login');
    }

    /**
     * Handle user with no active subscription
     */
    protected function handleNoActiveSubscription(Request $request, ?string $feature): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'status' => 'error',
                'message' => 'An active subscription is required to access this feature.',
                'error_code' => 'NO_ACTIVE_SUBSCRIPTION',
                'feature' => $feature,
                'upgrade_url' => '/app/subscription/plans',
            ], 403);
        }

        return redirect('/app/subscription/plans')
            ->with('warning', 'An active subscription is required to access this feature.');
    }

    /**
     * Handle insufficient feature access
     */
    protected function handleInsufficientAccess(Request $request, string $feature): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'status' => 'error',
                'message' => 'Your current subscription plan does not include access to this feature.',
                'error_code' => 'INSUFFICIENT_SUBSCRIPTION_ACCESS',
                'feature' => $feature,
                'upgrade_url' => '/app/subscription/upgrade',
            ], 403);
        }

        return redirect('/app/subscription/upgrade')
            ->with('warning', 'Your current subscription plan does not include access to this feature.');
    }

    /**
     * Handle insufficient tier access
     */
    protected function handleInsufficientTier(Request $request, string $tier): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'status' => 'error',
                'message' => "This feature requires a {$tier} subscription or higher.",
                'error_code' => 'INSUFFICIENT_SUBSCRIPTION_TIER',
                'required_tier' => $tier,
                'upgrade_url' => '/app/subscription/upgrade',
            ], 403);
        }

        return redirect('/app/subscription/upgrade')
            ->with('warning', "This feature requires a {$tier} subscription or higher.");
    }

    /**
     * Handle usage limit exceeded
     */
    protected function handleLimitExceeded(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'status' => 'error',
                'message' => 'You have reached your monthly usage limit for this feature.',
                'error_code' => 'USAGE_LIMIT_EXCEEDED',
                'upgrade_url' => '/app/subscription/upgrade',
            ], 403);
        }

        return redirect('/app/subscription/upgrade')
            ->with('warning', 'You have reached your monthly usage limit for this feature.');
    }
}
