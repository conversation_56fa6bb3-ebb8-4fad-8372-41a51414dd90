import { test, expect } from '@playwright/test';

test.describe('Subscription System', () => {
    test.beforeEach(async ({ page }) => {
        // Navigate to the React app
        await page.goto('http://localhost:5176');
    });

    test('should display subscription plans page', async ({ page }) => {
        // Navigate to login first since routes are protected
        await page.goto('http://localhost:5176/app/login');

        // Check if login page loads
        await expect(page.locator('text=Login')).toBeVisible({ timeout: 10000 });

        // For now, just check that the app is loading properly
        // In a real test, we would login first
        console.log('Login page loaded successfully');
    });

    test('should show login page when accessing protected routes', async ({ page }) => {
        // Navigate to investor dashboard (should redirect to login)
        await page.goto('http://localhost:5176/app/investor/dashboard');

        // Should redirect to login page
        await expect(page.locator('text=Login')).toBeVisible({ timeout: 10000 });
        console.log('Protected route correctly redirects to login');
    });

    test('should display subscription gates on discovery page', async ({ page }) => {
        // Navigate to discovery page
        await page.goto('http://localhost:5176/app/discovery');
        
        // Check if basic search is available
        await expect(page.locator('text=Basic Search')).toBeVisible();
        await expect(page.locator('input[placeholder="Enter keywords..."]')).toBeVisible();
        
        // Check if premium features are gated
        await expect(page.locator('text=Upgrade Required')).toBeVisible();
        await expect(page.locator('text=This feature requires a Premium plan')).toBeVisible();
        
        // Check if enterprise features are gated
        await expect(page.locator('text=This feature requires a Enterprise plan')).toBeVisible();
    });

    test('should display subscription gates on interest requests page', async ({ page }) => {
        // Navigate to interest requests page
        await page.goto('http://localhost:5176/app/interest-requests');
        
        // Check if basic functionality is available
        await expect(page.locator('text=Recent Requests')).toBeVisible();
        
        // Check if premium analytics are gated
        await expect(page.locator('text=This feature requires a Premium plan')).toBeVisible();
        
        // Check if enterprise bulk actions are gated
        await expect(page.locator('text=This feature requires a Enterprise plan')).toBeVisible();
    });

    test('should display subscription gates on investor profile', async ({ page }) => {
        // Navigate to investor profile page
        await page.goto('http://localhost:5176/app/investor/profile');
        
        // Check if basic profile information is available
        await expect(page.locator('text=Basic Information')).toBeVisible();
        await expect(page.locator('input[placeholder="Enter your full name"]')).toBeVisible();
        
        // Check if premium portfolio management is gated
        await expect(page.locator('text=This feature requires a Premium plan')).toBeVisible();
        
        // Check if enterprise AI insights are gated
        await expect(page.locator('text=This feature requires a Enterprise plan')).toBeVisible();
    });

    test('should display subscription gates on startup profile', async ({ page }) => {
        // Navigate to startup profile page
        await page.goto('http://localhost:5176/app/startup/profile');
        
        // Check if basic company information is available
        await expect(page.locator('text=Company Information')).toBeVisible();
        await expect(page.locator('input[placeholder="Enter company name"]')).toBeVisible();
        
        // Check if premium media gallery is gated
        await expect(page.locator('text=This feature requires a Premium plan')).toBeVisible();
        
        // Check if enterprise analytics are gated
        await expect(page.locator('text=This feature requires a Enterprise plan')).toBeVisible();
    });

    test('should navigate to subscription plans from upgrade prompts', async ({ page }) => {
        // Navigate to discovery page
        await page.goto('http://localhost:5176/app/discovery');
        
        // Click on upgrade button in subscription gate
        await page.locator('a[href="/app/subscription/plans"]').first().click();
        
        // Should navigate to plans page
        await expect(page).toHaveURL('http://localhost:5176/app/subscription/plans');
        await expect(page.locator('h1')).toContainText('Subscription Plans');
    });

    test('should display manage subscription page', async ({ page }) => {
        // Navigate to manage subscription page
        await page.goto('http://localhost:5176/app/subscription/manage');
        
        // Check if the page loads
        await expect(page.locator('h1')).toContainText('Manage Subscription');
        
        // Check if no subscription message is displayed
        await expect(page.locator('text=No Active Subscription')).toBeVisible();
        await expect(page.locator('text=You don\'t have an active subscription')).toBeVisible();
    });

    test('should handle responsive design on mobile', async ({ page }) => {
        // Set mobile viewport
        await page.setViewportSize({ width: 375, height: 667 });
        
        // Navigate to subscription plans
        await page.goto('http://localhost:5176/app/subscription/plans');
        
        // Check if plans are still visible on mobile
        await expect(page.locator('text=Basic Plan')).toBeVisible();
        await expect(page.locator('text=Premium Plan')).toBeVisible();
        await expect(page.locator('text=Enterprise Plan')).toBeVisible();
        
        // Check if pricing is still visible
        await expect(page.locator('text=$29')).toBeVisible();
    });

    test('should handle responsive design on tablet', async ({ page }) => {
        // Set tablet viewport
        await page.setViewportSize({ width: 768, height: 1024 });
        
        // Navigate to discovery page
        await page.goto('http://localhost:5176/app/discovery');
        
        // Check if subscription gates are properly displayed
        await expect(page.locator('text=Upgrade Required')).toBeVisible();
        await expect(page.locator('text=Basic Search')).toBeVisible();
    });

    test('should handle responsive design on desktop', async ({ page }) => {
        // Set desktop viewport
        await page.setViewportSize({ width: 1024, height: 768 });
        
        // Navigate to investor dashboard
        await page.goto('http://localhost:5176/app/investor/dashboard');
        
        // Check if subscription status is properly displayed
        await expect(page.locator('text=No Active Subscription')).toBeVisible();
        await expect(page.locator('a[href="/app/subscription/plans"]')).toBeVisible();
    });
});
