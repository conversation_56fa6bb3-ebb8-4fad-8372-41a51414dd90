import { test, expect } from '@playwright/test';

test.describe('React App Basic Functionality', () => {
    test('should load the React app and show login page', async ({ page }) => {
        // Navigate to the React app
        await page.goto('http://localhost:5176/app');
        
        // Should redirect to login page
        await expect(page).toHaveURL('http://localhost:5176/app/login');
        
        // Check if login page elements are visible
        await expect(page.locator('text=Login')).toBeVisible({ timeout: 10000 });
        
        console.log('React app loaded successfully and shows login page');
    });

    test('should show register page when navigating to register', async ({ page }) => {
        // Navigate to register page
        await page.goto('http://localhost:5176/app/register');
        
        // Check if register page loads
        await expect(page.locator('text=Register')).toBeVisible({ timeout: 10000 });
        
        console.log('Register page loaded successfully');
    });

    test('should handle responsive design', async ({ page }) => {
        // Set mobile viewport
        await page.setViewportSize({ width: 375, height: 667 });
        
        // Navigate to the app
        await page.goto('http://localhost:5176/app');
        
        // Should still show login page on mobile
        await expect(page.locator('text=Login')).toBeVisible({ timeout: 10000 });
        
        console.log('Mobile responsive design working');
    });

    test('should verify subscription system components exist', async ({ page }) => {
        // Navigate to the app
        await page.goto('http://localhost:5176/app');
        
        // Check if the app loads without JavaScript errors
        const errors = [];
        page.on('console', msg => {
            if (msg.type() === 'error') {
                errors.push(msg.text());
            }
        });
        
        // Wait a bit for any errors to surface
        await page.waitForTimeout(2000);
        
        // Check that there are no critical JavaScript errors
        const criticalErrors = errors.filter(error => 
            !error.includes('favicon') && 
            !error.includes('404') &&
            !error.includes('net::ERR_')
        );
        
        expect(criticalErrors.length).toBe(0);
        
        console.log('No critical JavaScript errors found');
    });
});
