# Page snapshot

```yaml
- link:
  - /url: https://laravel.com
  - img
- img
- link:
  - /url: https://vitejs.dev
  - img
- paragraph: This is the Vite development server that provides Hot Module Replacement for your Laravel application.
- paragraph: To access your Laravel application, you will need to run a local development server.
- heading "Artisan Serve" [level=2]:
  - link "Artisan Serve":
    - /url: https://laravel.com/docs/9.x/installation#your-first-laravel-project
- paragraph: Laravel's local development server powered by PHP's built-in web server.
- heading "Laravel Sail" [level=2]:
  - link "Laravel Sail":
    - /url: https://laravel.com/docs/sail
- paragraph: A light-weight command-line interface for interacting with Laravel's default Docker development environment.
- paragraph:
  - text: Your Laravel application's configured
  - code: APP_URL
  - text: "is:"
  - link "http://impactintels.test":
    - /url: http://impactintels.test
- paragraph: Want more information on Lara<PERSON>'s Vite integration?
- paragraph:
  - link "Read the docs →":
    - /url: https://laravel.com/docs/vite
```