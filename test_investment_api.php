<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Category;
use Illuminate\Support\Facades\Hash;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "Testing Investment Platform API...\n\n";

// Create or find test user
$user = User::where('email', '<EMAIL>')->first();
if (!$user) {
    $user = User::create([
        'name' => 'Test Investor',
        'email' => '<EMAIL>',
        'password' => Hash::make('password'),
        'email_verified_at' => now(),
    ]);
    $user->assignRole('investor');
} else {
    // Ensure user has the investor role
    if (!$user->hasRole('investor')) {
        $user->assignRole('investor');
    }
}

// Create API token
$token = $user->createToken('test-token')->plainTextToken;

echo "Created test user with token: " . substr($token, 0, 20) . "...\n\n";

// Test Categories endpoint
echo "Testing Categories endpoint...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/categories');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 200) . "...\n\n";

// Test Investor Profile Creation
echo "Testing Investor Profile Creation...\n";
$profileData = [
    'investment_budget_min' => 10000,
    'investment_budget_max' => 100000,
    'risk_tolerance' => 'medium',
    'investment_experience' => 'intermediate',
    'bio' => 'Test investor profile',
    'website' => 'https://example.com',
    'linkedin' => 'https://linkedin.com/in/test',
    'investment_preferences' => ['sustainability', 'technology'],
    'categories' => Category::take(2)->pluck('id')->toArray(),
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/investor-profiles');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($profileData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 300) . "...\n\n";

// Test ESG Questions endpoint
echo "Testing ESG Questions endpoint...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/esg/questions');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 200) . "...\n\n";

// Test Discovery endpoint
echo "Testing Discovery endpoint...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8001/api/investment/discovery/startups');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: application/json',
    'Content-Type: application/json',
    'Authorization: Bearer ' . $token
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: " . substr($response, 0, 200) . "...\n\n";

// Cleanup
$user->tokens()->delete();

echo "Test completed! Cleaned up tokens.\n";
